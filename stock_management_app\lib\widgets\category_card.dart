import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/models.dart';
import '../providers/providers.dart';
import '../constants/app_constants.dart';
import '../constants/app_theme.dart';

/// ويدجت بطاقة الفئة
/// Category Card Widget for displaying category information
class CategoryCard extends StatelessWidget {
  final Category category;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const CategoryCard({
    super.key,
    required this.category,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              if (category.description != null && category.description!.isNotEmpty) ...[
                const SizedBox(height: AppConstants.smallPadding),
                _buildDescription(context),
              ],
              const SizedBox(height: AppConstants.smallPadding),
              _buildInfo(context),
              if (_hasPermissions(context)) ...[
                const SizedBox(height: AppConstants.smallPadding),
                _buildActions(context),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        _buildCategoryIcon(),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: Text(
            category.name,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        _buildProductCount(),
      ],
    );
  }

  Widget _buildCategoryIcon() {
    final color = category.color != null
        ? Color(int.parse(category.color!.replaceFirst('#', '0xFF')))
        : AppTheme.primaryColor;

    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Icon(
        Icons.category,
        color: color,
        size: 24,
      ),
    );
  }

  Widget _buildProductCount() {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        final productCount = productProvider.getProductsByCategory(category.id!).length;
        
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.primaryColor.withOpacity(0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.inventory_2,
                size: 12,
                color: AppTheme.primaryColor,
              ),
              const SizedBox(width: 4),
              Text(
                '$productCount',
                style: const TextStyle(
                  color: AppTheme.primaryColor,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDescription(BuildContext context) {
    return Text(
      category.description!,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: AppTheme.textSecondary,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildInfo(BuildContext context) {
    return Row(
      children: [
        const Icon(
          Icons.access_time,
          size: 16,
          color: AppTheme.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          'تم الإنشاء: ${_formatDate(category.createdAt)}',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppTheme.textSecondary,
          ),
        ),
        if (category.updatedAt != null) ...[
          const SizedBox(width: AppConstants.defaultPadding),
          const Icon(
            Icons.update,
            size: 16,
            color: AppTheme.textSecondary,
          ),
          const SizedBox(width: 4),
          Text(
            'آخر تحديث: ${_formatDate(category.updatedAt!)}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (onEdit != null)
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              if (authProvider.hasPermission('edit_product')) {
                return TextButton.icon(
                  onPressed: onEdit,
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('تعديل'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppTheme.primaryColor,
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        if (onDelete != null)
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              if (authProvider.hasPermission('delete_product')) {
                return TextButton.icon(
                  onPressed: onDelete,
                  icon: const Icon(Icons.delete, size: 16),
                  label: const Text('حذف'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppTheme.errorColor,
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
      ],
    );
  }

  bool _hasPermissions(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.hasPermission('edit_product') || 
           authProvider.hasPermission('delete_product');
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
