import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/models.dart';
import '../constants/app_constants.dart';

/// مساعد قاعدة البيانات
/// Database Helper for SQLite operations
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  /// الحصول على قاعدة البيانات
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), AppConstants.databaseName);
    
    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  /// إنشاء الجداول
  Future<void> _onCreate(Database db, int version) async {
    // جدول المستخدمين
    await db.execute('''
      CREATE TABLE ${AppConstants.usersTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        full_name TEXT NOT NULL,
        role INTEGER NOT NULL,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        last_login TEXT
      )
    ''');

    // جدول الفئات
    await db.execute('''
      CREATE TABLE ${AppConstants.categoriesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        description TEXT,
        color TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // جدول المنتجات
    await db.execute('''
      CREATE TABLE ${AppConstants.productsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        barcode TEXT UNIQUE,
        category_id INTEGER NOT NULL,
        purchase_price REAL NOT NULL,
        selling_price REAL NOT NULL,
        quantity INTEGER NOT NULL DEFAULT 0,
        min_quantity INTEGER DEFAULT 5,
        image_path TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (category_id) REFERENCES ${AppConstants.categoriesTable} (id)
      )
    ''');

    // جدول سجل العمليات
    await db.execute('''
      CREATE TABLE ${AppConstants.transactionLogsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        type INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        previous_quantity INTEGER NOT NULL,
        new_quantity INTEGER NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (product_id) REFERENCES ${AppConstants.productsTable} (id),
        FOREIGN KEY (user_id) REFERENCES ${AppConstants.usersTable} (id)
      )
    ''');

    // إنشاء فهارس لتحسين الأداء
    await db.execute('''
      CREATE INDEX idx_products_category ON ${AppConstants.productsTable} (category_id)
    ''');
    
    await db.execute('''
      CREATE INDEX idx_products_barcode ON ${AppConstants.productsTable} (barcode)
    ''');
    
    await db.execute('''
      CREATE INDEX idx_transaction_logs_product ON ${AppConstants.transactionLogsTable} (product_id)
    ''');
    
    await db.execute('''
      CREATE INDEX idx_transaction_logs_user ON ${AppConstants.transactionLogsTable} (user_id)
    ''');

    // إدراج البيانات الافتراضية
    await _insertDefaultData(db);
  }

  /// تحديث قاعدة البيانات
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // سيتم تنفيذ تحديثات قاعدة البيانات هنا في المستقبل
  }

  /// إدراج البيانات الافتراضية
  Future<void> _insertDefaultData(Database db) async {
    // إنشاء مستخدم مدير افتراضي
    await db.insert(AppConstants.usersTable, {
      'username': 'admin',
      'password': 'admin123', // في التطبيق الحقيقي يجب تشفير كلمة المرور
      'full_name': 'مدير النظام',
      'role': UserRole.admin.index,
      'is_active': 1,
      'created_at': DateTime.now().toIso8601String(),
    });

    // إنشاء موظف افتراضي
    await db.insert(AppConstants.usersTable, {
      'username': 'employee',
      'password': 'emp123',
      'full_name': 'موظف المخزن',
      'role': UserRole.employee.index,
      'is_active': 1,
      'created_at': DateTime.now().toIso8601String(),
    });

    // إدراج الفئات الافتراضية
    for (Category category in DefaultCategories.defaultCategories) {
      await db.insert(AppConstants.categoriesTable, category.toMap());
    }
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  /// حذف قاعدة البيانات
  Future<void> deleteDatabase() async {
    String path = join(await getDatabasesPath(), AppConstants.databaseName);
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }

  /// تنفيذ استعلام خام
  Future<List<Map<String, dynamic>>> rawQuery(String sql, [List<Object?>? arguments]) async {
    final db = await database;
    return await db.rawQuery(sql, arguments);
  }

  /// تنفيذ أمر خام
  Future<int> rawInsert(String sql, [List<Object?>? arguments]) async {
    final db = await database;
    return await db.rawInsert(sql, arguments);
  }

  /// تنفيذ تحديث خام
  Future<int> rawUpdate(String sql, [List<Object?>? arguments]) async {
    final db = await database;
    return await db.rawUpdate(sql, arguments);
  }

  /// تنفيذ حذف خام
  Future<int> rawDelete(String sql, [List<Object?>? arguments]) async {
    final db = await database;
    return await db.rawDelete(sql, arguments);
  }

  /// بدء معاملة
  Future<T> transaction<T>(Future<T> Function(Transaction txn) action) async {
    final db = await database;
    return await db.transaction(action);
  }

  /// إنشاء نسخة احتياطية من البيانات
  Future<Map<String, List<Map<String, dynamic>>>> backup() async {
    final db = await database;
    
    return {
      'users': await db.query(AppConstants.usersTable),
      'categories': await db.query(AppConstants.categoriesTable),
      'products': await db.query(AppConstants.productsTable),
      'transaction_logs': await db.query(AppConstants.transactionLogsTable),
    };
  }

  /// استعادة البيانات من النسخة الاحتياطية
  Future<void> restore(Map<String, List<Map<String, dynamic>>> backupData) async {
    final db = await database;
    
    await db.transaction((txn) async {
      // حذف البيانات الحالية
      await txn.delete(AppConstants.transactionLogsTable);
      await txn.delete(AppConstants.productsTable);
      await txn.delete(AppConstants.categoriesTable);
      await txn.delete(AppConstants.usersTable);
      
      // استعادة البيانات
      for (var user in backupData['users'] ?? []) {
        await txn.insert(AppConstants.usersTable, user);
      }
      
      for (var category in backupData['categories'] ?? []) {
        await txn.insert(AppConstants.categoriesTable, category);
      }
      
      for (var product in backupData['products'] ?? []) {
        await txn.insert(AppConstants.productsTable, product);
      }
      
      for (var log in backupData['transaction_logs'] ?? []) {
        await txn.insert(AppConstants.transactionLogsTable, log);
      }
    });
  }

  /// الحصول على إحصائيات قاعدة البيانات
  Future<Map<String, int>> getStatistics() async {
    final db = await database;
    
    final usersCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM ${AppConstants.usersTable}')
    ) ?? 0;
    
    final categoriesCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM ${AppConstants.categoriesTable}')
    ) ?? 0;
    
    final productsCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM ${AppConstants.productsTable}')
    ) ?? 0;
    
    final transactionLogsCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM ${AppConstants.transactionLogsTable}')
    ) ?? 0;
    
    return {
      'users': usersCount,
      'categories': categoriesCount,
      'products': productsCount,
      'transaction_logs': transactionLogsCount,
    };
  }
}
