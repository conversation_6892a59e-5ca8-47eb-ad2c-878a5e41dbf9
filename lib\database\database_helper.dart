import 'dart:async';

import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

import '../constants/app_constants.dart';
import '../models/models.dart';

/// مساعد قاعدة البيانات
/// Database Helper for SQLite operations
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  /// الحصول على قاعدة البيانات
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), AppConstants.databaseName);

    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  /// إنشاء الجداول
  Future<void> _onCreate(Database db, int version) async {
    // جدول المستخدمين
    await db.execute('''
      CREATE TABLE ${AppConstants.usersTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        full_name TEXT NOT NULL,
        role INTEGER NOT NULL,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        last_login TEXT
      )
    ''');

    // جدول الفئات
    await db.execute('''
      CREATE TABLE ${AppConstants.categoriesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        description TEXT,
        color TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // جدول المنتجات
    await db.execute('''
      CREATE TABLE ${AppConstants.productsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        barcode TEXT UNIQUE,
        category_id INTEGER NOT NULL,
        purchase_price REAL NOT NULL,
        selling_price REAL NOT NULL,
        quantity INTEGER NOT NULL DEFAULT 0,
        min_quantity INTEGER DEFAULT 5,
        image_path TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (category_id) REFERENCES ${AppConstants.categoriesTable} (id)
      )
    ''');

    // جدول سجل العمليات
    await db.execute('''
      CREATE TABLE ${AppConstants.transactionLogsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        type INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        previous_quantity INTEGER NOT NULL,
        new_quantity INTEGER NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (product_id) REFERENCES ${AppConstants.productsTable} (id),
        FOREIGN KEY (user_id) REFERENCES ${AppConstants.usersTable} (id)
      )
    ''');

    // جدول المشتريات
    await db.execute('''
      CREATE TABLE ${AppConstants.purchasesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT UNIQUE NOT NULL,
        supplier_name TEXT NOT NULL,
        supplier_phone TEXT,
        supplier_address TEXT,
        total_amount REAL NOT NULL,
        paid_amount REAL DEFAULT 0.0,
        remaining_amount REAL NOT NULL,
        status INTEGER DEFAULT 0,
        purchase_date TEXT NOT NULL,
        notes TEXT,
        user_id INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (user_id) REFERENCES ${AppConstants.usersTable} (id)
      )
    ''');

    // جدول عناصر المشتريات
    await db.execute('''
      CREATE TABLE ${AppConstants.purchaseItemsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        purchase_id INTEGER NOT NULL,
        product_id INTEGER NOT NULL,
        product_name TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price REAL NOT NULL,
        total_price REAL NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (purchase_id) REFERENCES ${AppConstants.purchasesTable} (id),
        FOREIGN KEY (product_id) REFERENCES ${AppConstants.productsTable} (id)
      )
    ''');

    // جدول المبيعات
    await db.execute('''
      CREATE TABLE ${AppConstants.salesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT UNIQUE NOT NULL,
        customer_name TEXT,
        customer_phone TEXT,
        customer_address TEXT,
        total_amount REAL NOT NULL,
        paid_amount REAL DEFAULT 0.0,
        remaining_amount REAL NOT NULL,
        discount_amount REAL DEFAULT 0.0,
        status INTEGER DEFAULT 0,
        payment_method INTEGER DEFAULT 0,
        sale_date TEXT NOT NULL,
        notes TEXT,
        user_id INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (user_id) REFERENCES ${AppConstants.usersTable} (id)
      )
    ''');

    // جدول عناصر المبيعات
    await db.execute('''
      CREATE TABLE ${AppConstants.saleItemsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sale_id INTEGER NOT NULL,
        product_id INTEGER NOT NULL,
        product_name TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price REAL NOT NULL,
        total_price REAL NOT NULL,
        discount_amount REAL DEFAULT 0.0,
        created_at TEXT NOT NULL,
        FOREIGN KEY (sale_id) REFERENCES ${AppConstants.salesTable} (id),
        FOREIGN KEY (product_id) REFERENCES ${AppConstants.productsTable} (id)
      )
    ''');

    // جدول الصلاحيات
    await db.execute('''
      CREATE TABLE ${AppConstants.permissionsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        display_name TEXT NOT NULL,
        description TEXT NOT NULL,
        category TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // جدول صلاحيات المستخدمين
    await db.execute('''
      CREATE TABLE ${AppConstants.userPermissionsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        permission_id INTEGER NOT NULL,
        is_granted INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (user_id) REFERENCES ${AppConstants.usersTable} (id),
        FOREIGN KEY (permission_id) REFERENCES ${AppConstants.permissionsTable} (id),
        UNIQUE(user_id, permission_id)
      )
    ''');

    // إنشاء فهارس لتحسين الأداء
    await db.execute('''
      CREATE INDEX idx_products_category ON ${AppConstants.productsTable} (category_id)
    ''');

    await db.execute('''
      CREATE INDEX idx_products_barcode ON ${AppConstants.productsTable} (barcode)
    ''');

    await db.execute('''
      CREATE INDEX idx_transaction_logs_product ON ${AppConstants.transactionLogsTable} (product_id)
    ''');

    await db.execute('''
      CREATE INDEX idx_purchases_date ON ${AppConstants.purchasesTable} (purchase_date)
    ''');

    await db.execute('''
      CREATE INDEX idx_purchase_items_purchase ON ${AppConstants.purchaseItemsTable} (purchase_id)
    ''');

    await db.execute('''
      CREATE INDEX idx_sales_date ON ${AppConstants.salesTable} (sale_date)
    ''');

    await db.execute('''
      CREATE INDEX idx_sale_items_sale ON ${AppConstants.saleItemsTable} (sale_id)
    ''');

    await db.execute('''
      CREATE INDEX idx_permissions_name ON ${AppConstants.permissionsTable} (name)
    ''');

    await db.execute('''
      CREATE INDEX idx_user_permissions_user ON ${AppConstants.userPermissionsTable} (user_id)
    ''');

    await db.execute('''
      CREATE INDEX idx_user_permissions_permission ON ${AppConstants.userPermissionsTable} (permission_id)
    ''');

    await db.execute('''
      CREATE INDEX idx_transaction_logs_user ON ${AppConstants.transactionLogsTable} (user_id)
    ''');

    // إدراج البيانات الافتراضية
    await _insertDefaultData(db);
  }

  /// تحديث قاعدة البيانات
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // حذف الفهارس المكررة وإعادة إنشاء قاعدة البيانات
      await db.execute('DROP INDEX IF EXISTS idx_transaction_logs_product');
      await db.execute('DROP INDEX IF EXISTS idx_transaction_logs_user');

      // إعادة إنشاء الفهارس
      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_transaction_logs_product ON ${AppConstants.transactionLogsTable} (product_id)
      ''');

      await db.execute('''
        CREATE INDEX IF NOT EXISTS idx_transaction_logs_user ON ${AppConstants.transactionLogsTable} (user_id)
      ''');
    }
  }

  /// إدراج البيانات الافتراضية
  Future<void> _insertDefaultData(Database db) async {
    // إنشاء مستخدم مدير افتراضي
    await db.insert(AppConstants.usersTable, {
      'username': 'admin',
      'password': 'admin123', // في التطبيق الحقيقي يجب تشفير كلمة المرور
      'full_name': 'مدير النظام',
      'role': UserRole.admin.index,
      'is_active': 1,
      'created_at': DateTime.now().toIso8601String(),
    });

    // إنشاء موظف افتراضي
    await db.insert(AppConstants.usersTable, {
      'username': 'employee',
      'password': 'emp123',
      'full_name': 'موظف المخزن',
      'role': UserRole.employee.index,
      'is_active': 1,
      'created_at': DateTime.now().toIso8601String(),
    });

    // إدراج الفئات الافتراضية
    for (Category category in DefaultCategories.defaultCategories) {
      await db.insert(AppConstants.categoriesTable, category.toMap());
    }

    // إدراج الصلاحيات الافتراضية
    final defaultPermissions = DefaultPermissions.getDefaultPermissions();
    for (final permission in defaultPermissions) {
      await db.insert(
        AppConstants.permissionsTable,
        permission.toMap(),
        conflictAlgorithm: ConflictAlgorithm.ignore,
      );
    }

    // منح جميع الصلاحيات للمدير الافتراضي
    final adminPermissions = await db.query(AppConstants.permissionsTable);
    for (final permission in adminPermissions) {
      await db.insert(
        AppConstants.userPermissionsTable,
        {
          'user_id': 1, // المدير الافتراضي
          'permission_id': permission['id'],
          'is_granted': 1,
          'created_at': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.ignore,
      );
    }

    // منح صلاحيات أساسية للموظف الافتراضي
    final basicPermissions = [
      'view_products',
      'view_categories',
      'view_purchases',
      'view_sales',
      'view_settings',
    ];

    for (final permissionName in basicPermissions) {
      final permission = await db.query(
        AppConstants.permissionsTable,
        where: 'name = ?',
        whereArgs: [permissionName],
      );

      if (permission.isNotEmpty) {
        await db.insert(
          AppConstants.userPermissionsTable,
          {
            'user_id': 2, // الموظف الافتراضي
            'permission_id': permission.first['id'],
            'is_granted': 1,
            'created_at': DateTime.now().toIso8601String(),
          },
          conflictAlgorithm: ConflictAlgorithm.ignore,
        );
      }
    }
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  /// حذف قاعدة البيانات
  Future<void> deleteDatabase() async {
    String path = join(await getDatabasesPath(), AppConstants.databaseName);
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }

  /// إعادة تعيين قاعدة البيانات (حذف وإعادة إنشاء)
  Future<void> resetDatabase() async {
    await close();
    await deleteDatabase();
    _database = null;
    // إعادة إنشاء قاعدة البيانات
    await database;
  }

  /// تنفيذ استعلام خام
  Future<List<Map<String, dynamic>>> rawQuery(
    String sql, [
    List<Object?>? arguments,
  ]) async {
    final db = await database;
    return await db.rawQuery(sql, arguments);
  }

  /// تنفيذ أمر خام
  Future<int> rawInsert(String sql, [List<Object?>? arguments]) async {
    final db = await database;
    return await db.rawInsert(sql, arguments);
  }

  /// تنفيذ تحديث خام
  Future<int> rawUpdate(String sql, [List<Object?>? arguments]) async {
    final db = await database;
    return await db.rawUpdate(sql, arguments);
  }

  /// تنفيذ حذف خام
  Future<int> rawDelete(String sql, [List<Object?>? arguments]) async {
    final db = await database;
    return await db.rawDelete(sql, arguments);
  }

  /// بدء معاملة
  Future<T> transaction<T>(Future<T> Function(Transaction txn) action) async {
    final db = await database;
    return await db.transaction(action);
  }

  /// إنشاء نسخة احتياطية من البيانات
  Future<Map<String, List<Map<String, dynamic>>>> backup() async {
    final db = await database;

    return {
      'users': await db.query(AppConstants.usersTable),
      'categories': await db.query(AppConstants.categoriesTable),
      'products': await db.query(AppConstants.productsTable),
      'transaction_logs': await db.query(AppConstants.transactionLogsTable),
    };
  }

  /// استعادة البيانات من النسخة الاحتياطية
  Future<void> restore(
    Map<String, List<Map<String, dynamic>>> backupData,
  ) async {
    final db = await database;

    await db.transaction((txn) async {
      // حذف البيانات الحالية
      await txn.delete(AppConstants.transactionLogsTable);
      await txn.delete(AppConstants.productsTable);
      await txn.delete(AppConstants.categoriesTable);
      await txn.delete(AppConstants.usersTable);

      // استعادة البيانات
      for (var user in backupData['users'] ?? []) {
        await txn.insert(AppConstants.usersTable, user);
      }

      for (var category in backupData['categories'] ?? []) {
        await txn.insert(AppConstants.categoriesTable, category);
      }

      for (var product in backupData['products'] ?? []) {
        await txn.insert(AppConstants.productsTable, product);
      }

      for (var log in backupData['transaction_logs'] ?? []) {
        await txn.insert(AppConstants.transactionLogsTable, log);
      }
    });
  }

  /// الحصول على إحصائيات قاعدة البيانات
  Future<Map<String, int>> getStatistics() async {
    final db = await database;

    final usersCount =
        Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM ${AppConstants.usersTable}'),
        ) ??
        0;

    final categoriesCount =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT COUNT(*) FROM ${AppConstants.categoriesTable}',
          ),
        ) ??
        0;

    final productsCount =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT COUNT(*) FROM ${AppConstants.productsTable}',
          ),
        ) ??
        0;

    final transactionLogsCount =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT COUNT(*) FROM ${AppConstants.transactionLogsTable}',
          ),
        ) ??
        0;

    return {
      'users': usersCount,
      'categories': categoriesCount,
      'products': productsCount,
      'transaction_logs': transactionLogsCount,
    };
  }
}
