import 'package:flutter/material.dart';
import '../models/models.dart';
import '../constants/app_constants.dart';
import '../constants/app_theme.dart';

/// أنواع تنبيهات المخزون
enum StockAlertType {
  lowStock,
  outOfStock,
}

/// ويدجت بطاقة تنبيهات المخزون
/// Stock Alert Card Widget for displaying inventory alerts
class StockAlertCard extends StatelessWidget {
  final String title;
  final List<Product> products;
  final StockAlertType alertType;
  final VoidCallback? onViewAll;

  const StockAlertCard({
    super.key,
    required this.title,
    required this.products,
    required this.alertType,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    if (products.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: AppConstants.defaultPadding),
            ...products.map((product) => _buildProductItem(context, product)),
            if (onViewAll != null) ...[
              const SizedBox(height: AppConstants.smallPadding),
              _buildViewAllButton(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final color = _getAlertColor();
    final icon = _getAlertIcon();

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Text(
            '${products.length}',
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProductItem(BuildContext context, Product product) {
    final color = _getAlertColor();

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product.name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (product.barcode != null && product.barcode!.isNotEmpty)
                  Text(
                    'الباركود: ${product.barcode}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '${product.quantity}',
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildViewAllButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: TextButton(
        onPressed: onViewAll,
        child: const Text('عرض الكل'),
      ),
    );
  }

  Color _getAlertColor() {
    switch (alertType) {
      case StockAlertType.lowStock:
        return AppTheme.warningColor;
      case StockAlertType.outOfStock:
        return AppTheme.errorColor;
    }
  }

  IconData _getAlertIcon() {
    switch (alertType) {
      case StockAlertType.lowStock:
        return Icons.warning;
      case StockAlertType.outOfStock:
        return Icons.error;
    }
  }
}
