import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';

/// شاشة إضافة/تعديل المنتج
/// Add/Edit Product Screen
class AddEditProductScreen extends StatefulWidget {
  final Product? product;

  const AddEditProductScreen({super.key, this.product});

  @override
  State<AddEditProductScreen> createState() => _AddEditProductScreenState();
}

class _AddEditProductScreenState extends State<AddEditProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _purchasePriceController = TextEditingController();
  final _sellingPriceController = TextEditingController();
  final _quantityController = TextEditingController();
  final _minQuantityController = TextEditingController();
  
  int? _selectedCategoryId;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.product != null) {
      _nameController.text = widget.product!.name;
      _descriptionController.text = widget.product!.description ?? '';
      _barcodeController.text = widget.product!.barcode ?? '';
      _purchasePriceController.text = widget.product!.purchasePrice.toString();
      _sellingPriceController.text = widget.product!.sellingPrice.toString();
      _quantityController.text = widget.product!.quantity.toString();
      _minQuantityController.text = widget.product!.minQuantity.toString();
      _selectedCategoryId = widget.product!.categoryId;
    } else {
      _minQuantityController.text = AppConstants.defaultMinQuantity.toString();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _barcodeController.dispose();
    _purchasePriceController.dispose();
    _sellingPriceController.dispose();
    _quantityController.dispose();
    _minQuantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.product != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'تعديل المنتج' : 'إضافة منتج جديد'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProduct,
            child: Text(
              isEditing ? 'تحديث' : 'حفظ',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildPricingSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildInventorySection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildSaveButton(isEditing),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الأساسية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildNameField(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildDescriptionField(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildCategoryDropdown(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildBarcodeField(),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الأسعار',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(child: _buildPurchasePriceField()),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(child: _buildSellingPriceField()),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            _buildProfitMarginDisplay(),
          ],
        ),
      ),
    );
  }

  Widget _buildInventorySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المخزون',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(child: _buildQuantityField()),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(child: _buildMinQuantityField()),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: const InputDecoration(
        labelText: 'اسم المنتج *',
        hintText: 'أدخل اسم المنتج',
        prefixIcon: Icon(Icons.inventory_2),
      ),
      textInputAction: TextInputAction.next,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return AppConstants.requiredFieldMessage;
        }
        return null;
      },
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      decoration: const InputDecoration(
        labelText: 'الوصف',
        hintText: 'أدخل وصف المنتج (اختياري)',
        prefixIcon: Icon(Icons.description),
      ),
      maxLines: 3,
      textInputAction: TextInputAction.next,
    );
  }

  Widget _buildCategoryDropdown() {
    return Consumer<CategoryProvider>(
      builder: (context, categoryProvider, child) {
        return DropdownButtonFormField<int>(
          value: _selectedCategoryId,
          decoration: const InputDecoration(
            labelText: 'الفئة *',
            prefixIcon: Icon(Icons.category),
          ),
          items: categoryProvider.categories.map((category) {
            return DropdownMenuItem<int>(
              value: category.id,
              child: Text(category.name),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCategoryId = value;
            });
          },
          validator: (value) {
            if (value == null) {
              return 'يرجى اختيار فئة';
            }
            return null;
          },
        );
      },
    );
  }

  Widget _buildBarcodeField() {
    return TextFormField(
      controller: _barcodeController,
      decoration: InputDecoration(
        labelText: 'الباركود',
        hintText: 'أدخل الباركود (اختياري)',
        prefixIcon: const Icon(Icons.qr_code),
        suffixIcon: IconButton(
          icon: const Icon(Icons.qr_code_scanner),
          onPressed: _scanBarcode,
        ),
      ),
      textInputAction: TextInputAction.next,
    );
  }

  Widget _buildPurchasePriceField() {
    return TextFormField(
      controller: _purchasePriceController,
      decoration: const InputDecoration(
        labelText: 'سعر الشراء *',
        hintText: '0.00',
        prefixIcon: Icon(Icons.attach_money),
        suffixText: AppConstants.currencySymbol,
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      textInputAction: TextInputAction.next,
      onChanged: (_) => setState(() {}),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return AppConstants.requiredFieldMessage;
        }
        final price = double.tryParse(value);
        if (price == null || price < 0) {
          return AppConstants.invalidNumberMessage;
        }
        return null;
      },
    );
  }

  Widget _buildSellingPriceField() {
    return TextFormField(
      controller: _sellingPriceController,
      decoration: const InputDecoration(
        labelText: 'سعر البيع *',
        hintText: '0.00',
        prefixIcon: Icon(Icons.sell),
        suffixText: AppConstants.currencySymbol,
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      textInputAction: TextInputAction.next,
      onChanged: (_) => setState(() {}),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return AppConstants.requiredFieldMessage;
        }
        final price = double.tryParse(value);
        if (price == null || price < 0) {
          return AppConstants.invalidNumberMessage;
        }
        
        final purchasePrice = double.tryParse(_purchasePriceController.text) ?? 0;
        if (price < purchasePrice) {
          return 'سعر البيع يجب أن يكون أكبر من سعر الشراء';
        }
        
        return null;
      },
    );
  }

  Widget _buildProfitMarginDisplay() {
    final purchasePrice = double.tryParse(_purchasePriceController.text) ?? 0;
    final sellingPrice = double.tryParse(_sellingPriceController.text) ?? 0;
    
    if (purchasePrice > 0 && sellingPrice > 0) {
      final profitMargin = ((sellingPrice - purchasePrice) / purchasePrice) * 100;
      final profit = sellingPrice - purchasePrice;
      
      return Container(
        padding: const EdgeInsets.all(AppConstants.smallPadding),
        decoration: BoxDecoration(
          color: AppTheme.successColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.successColor.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الربح: ${profit.toStringAsFixed(2)} ${AppConstants.currencySymbol}',
              style: const TextStyle(
                color: AppTheme.successColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              'هامش الربح: ${profitMargin.toStringAsFixed(1)}%',
              style: const TextStyle(
                color: AppTheme.successColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    }
    
    return const SizedBox.shrink();
  }

  Widget _buildQuantityField() {
    return TextFormField(
      controller: _quantityController,
      decoration: const InputDecoration(
        labelText: 'الكمية *',
        hintText: '0',
        prefixIcon: Icon(Icons.inventory),
      ),
      keyboardType: TextInputType.number,
      textInputAction: TextInputAction.next,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return AppConstants.requiredFieldMessage;
        }
        final quantity = int.tryParse(value);
        if (quantity == null || quantity < 0) {
          return AppConstants.invalidNumberMessage;
        }
        return null;
      },
    );
  }

  Widget _buildMinQuantityField() {
    return TextFormField(
      controller: _minQuantityController,
      decoration: const InputDecoration(
        labelText: 'الحد الأدنى *',
        hintText: '5',
        prefixIcon: Icon(Icons.warning),
      ),
      keyboardType: TextInputType.number,
      textInputAction: TextInputAction.done,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return AppConstants.requiredFieldMessage;
        }
        final minQuantity = int.tryParse(value);
        if (minQuantity == null || minQuantity < 0) {
          return AppConstants.invalidNumberMessage;
        }
        return null;
      },
    );
  }

  Widget _buildSaveButton(bool isEditing) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveProduct,
        child: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(isEditing ? 'تحديث المنتج' : 'إضافة المنتج'),
      ),
    );
  }

  void _scanBarcode() {
    // سيتم إضافة وظيفة مسح الباركود لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ماسح الباركود قريباً'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final productProvider = Provider.of<ProductProvider>(context, listen: false);
      
      final product = Product(
        id: widget.product?.id,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isNotEmpty 
            ? _descriptionController.text.trim() 
            : null,
        barcode: _barcodeController.text.trim().isNotEmpty 
            ? _barcodeController.text.trim() 
            : null,
        categoryId: _selectedCategoryId!,
        purchasePrice: double.parse(_purchasePriceController.text),
        sellingPrice: double.parse(_sellingPriceController.text),
        quantity: int.parse(_quantityController.text),
        minQuantity: int.parse(_minQuantityController.text),
        createdAt: widget.product?.createdAt ?? DateTime.now(),
        updatedAt: widget.product != null ? DateTime.now() : null,
      );

      bool success;
      if (widget.product != null) {
        success = await productProvider.updateProduct(product);
      } else {
        success = await productProvider.addProduct(product);
      }

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.product != null 
                    ? 'تم تحديث المنتج بنجاح'
                    : 'تم إضافة المنتج بنجاح',
              ),
              backgroundColor: AppTheme.successColor,
            ),
          );
          Navigator.of(context).pop();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                productProvider.errorMessage ?? 
                (widget.product != null 
                    ? 'فشل في تحديث المنتج'
                    : 'فشل في إضافة المنتج'),
              ),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
