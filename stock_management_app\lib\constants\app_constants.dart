/// ثوابت التطبيق
/// Application Constants
library;

class AppConstants {
  // معلومات التطبيق
  static const String appName = 'إدارة المخازن';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق إدارة المخازن والمنتجات';

  // قاعدة البيانات
  static const String databaseName = 'stock_management.db';
  static const int databaseVersion = 1;

  // أسماء الجداول
  static const String usersTable = 'users';
  static const String categoriesTable = 'categories';
  static const String productsTable = 'products';
  static const String transactionLogsTable = 'transaction_logs';

  // مفاتيح التخزين المحلي
  static const String currentUserKey = 'current_user';
  static const String isLoggedInKey = 'is_logged_in';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';

  // إعدادات التطبيق
  static const int defaultMinQuantity = 5;
  static const int maxImageSizeMB = 5;
  static const List<String> supportedImageFormats = ['jpg', 'jpeg', 'png'];

  // أبعاد الشاشة
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 8.0;

  // أحجام الخط
  static const double titleFontSize = 24.0;
  static const double subtitleFontSize = 18.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 14.0;

  // الألوان
  static const String primaryColor = '#1976D2';
  static const String secondaryColor = '#03DAC6';
  static const String errorColor = '#B00020';
  static const String successColor = '#4CAF50';
  static const String warningColor = '#FF9800';

  // رسائل النظام
  static const String loginSuccessMessage = 'تم تسجيل الدخول بنجاح';
  static const String loginErrorMessage = 'خطأ في اسم المستخدم أو كلمة المرور';
  static const String logoutMessage = 'تم تسجيل الخروج بنجاح';
  static const String saveSuccessMessage = 'تم الحفظ بنجاح';
  static const String deleteSuccessMessage = 'تم الحذف بنجاح';
  static const String updateSuccessMessage = 'تم التحديث بنجاح';
  static const String errorMessage = 'حدث خطأ، يرجى المحاولة مرة أخرى';
  static const String noDataMessage = 'لا توجد بيانات';
  static const String loadingMessage = 'جاري التحميل...';

  // رسائل التحقق
  static const String requiredFieldMessage = 'هذا الحقل مطلوب';
  static const String invalidEmailMessage = 'البريد الإلكتروني غير صحيح';
  static const String passwordTooShortMessage = 'كلمة المرور قصيرة جداً';
  static const String confirmPasswordMessage = 'كلمات المرور غير متطابقة';
  static const String invalidNumberMessage = 'الرقم غير صحيح';
  static const String invalidBarcodeMessage = 'الباركود غير صحيح';

  // إعدادات الباركود
  static const String barcodeFormat = 'CODE_128';
  static const int barcodeLength = 13;

  // إعدادات التصدير
  static const String exportDateFormat = 'yyyy-MM-dd';
  static const String exportTimeFormat = 'HH:mm:ss';
  static const String exportFileName = 'stock_export';

  // حدود البحث والترقيم
  static const int searchMinLength = 2;
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // مدة انتظار الشبكة
  static const int networkTimeoutSeconds = 30;
  static const int retryAttempts = 3;

  // مسارات الملفات
  static const String imagesPath = 'assets/images/';
  static const String iconsPath = 'assets/icons/';
  static const String documentsPath = 'documents/';
  static const String exportsPath = 'exports/';

  // أنواع الملفات المدعومة للتصدير
  static const List<String> exportFormats = ['xlsx', 'csv', 'pdf'];

  // إعدادات الإشعارات
  static const String lowStockNotificationTitle = 'تنبيه: كمية قليلة';
  static const String outOfStockNotificationTitle = 'تنبيه: نفد المخزون';
  static const String newProductNotificationTitle = 'منتج جديد';

  // أذونات التطبيق
  static const List<String> requiredPermissions = [
    'CAMERA',
    'READ_EXTERNAL_STORAGE',
    'WRITE_EXTERNAL_STORAGE',
  ];

  // إعدادات الكاميرا
  static const double imageQuality = 0.8;
  static const int maxImageWidth = 1024;
  static const int maxImageHeight = 1024;

  // فترات التحديث
  static const int autoRefreshIntervalMinutes = 5;
  static const int dataBackupIntervalHours = 24;

  // حدود النظام
  static const int maxProductsPerCategory = 10000;
  static const int maxCategoriesCount = 100;
  static const int maxUsersCount = 50;
  static const int maxTransactionLogsCount = 100000;

  // رسائل الأخطاء المخصصة
  static const String networkErrorMessage = 'خطأ في الاتصال بالشبكة';
  static const String databaseErrorMessage = 'خطأ في قاعدة البيانات';
  static const String permissionDeniedMessage = 'تم رفض الإذن';
  static const String fileNotFoundMessage = 'الملف غير موجود';
  static const String invalidDataMessage = 'البيانات غير صحيحة';

  // إعدادات الأمان
  static const int sessionTimeoutMinutes = 30;
  static const int maxLoginAttempts = 5;
  static const int lockoutDurationMinutes = 15;

  // تنسيقات التاريخ والوقت
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String fullDateTimeFormat = 'EEEE، dd MMMM yyyy - HH:mm';

  // إعدادات العملة
  static const String currencySymbol = 'ر.س';
  static const String currencyCode = 'SAR';
  static const int decimalPlaces = 2;
}
