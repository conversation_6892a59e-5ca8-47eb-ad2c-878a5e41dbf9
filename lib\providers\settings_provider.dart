import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants/app_constants.dart';

/// مزود إعدادات التطبيق
/// Settings Provider for managing app settings
class SettingsProvider with ChangeNotifier {
  String _currencySymbol = AppConstants.currencySymbol;
  String _language = 'ar';
  String _theme = 'light';
  bool _notificationsEnabled = true;
  bool _stockAlertsEnabled = true;
  int _minStockAlert = AppConstants.defaultMinQuantity;
  bool _autoRefreshEnabled = true;
  int _autoRefreshInterval = AppConstants.autoRefreshIntervalMinutes;

  // Getters
  String get currencySymbol => _currencySymbol;
  String get language => _language;
  String get theme => _theme;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get stockAlertsEnabled => _stockAlertsEnabled;
  int get minStockAlert => _minStockAlert;
  bool get autoRefreshEnabled => _autoRefreshEnabled;
  int get autoRefreshInterval => _autoRefreshInterval;

  /// تحميل الإعدادات من التخزين المحلي
  Future<void> loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    _currencySymbol = prefs.getString(AppConstants.currencyKey) ?? AppConstants.currencySymbol;
    _language = prefs.getString(AppConstants.languageKey) ?? 'ar';
    _theme = prefs.getString(AppConstants.themeKey) ?? 'light';
    _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
    _stockAlertsEnabled = prefs.getBool('stock_alerts_enabled') ?? true;
    _minStockAlert = prefs.getInt('min_stock_alert') ?? AppConstants.defaultMinQuantity;
    _autoRefreshEnabled = prefs.getBool('auto_refresh_enabled') ?? true;
    _autoRefreshInterval = prefs.getInt('auto_refresh_interval') ?? AppConstants.autoRefreshIntervalMinutes;
    
    notifyListeners();
  }

  /// تغيير رمز العملة
  Future<void> setCurrencySymbol(String symbol) async {
    if (_currencySymbol != symbol) {
      _currencySymbol = symbol;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.currencyKey, symbol);
      
      notifyListeners();
    }
  }

  /// تغيير اللغة
  Future<void> setLanguage(String language) async {
    if (_language != language) {
      _language = language;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.languageKey, language);
      
      notifyListeners();
    }
  }

  /// تغيير المظهر
  Future<void> setTheme(String theme) async {
    if (_theme != theme) {
      _theme = theme;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.themeKey, theme);
      
      notifyListeners();
    }
  }

  /// تفعيل/إلغاء الإشعارات
  Future<void> setNotificationsEnabled(bool enabled) async {
    if (_notificationsEnabled != enabled) {
      _notificationsEnabled = enabled;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('notifications_enabled', enabled);
      
      notifyListeners();
    }
  }

  /// تفعيل/إلغاء تنبيهات المخزون
  Future<void> setStockAlertsEnabled(bool enabled) async {
    if (_stockAlertsEnabled != enabled) {
      _stockAlertsEnabled = enabled;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('stock_alerts_enabled', enabled);
      
      notifyListeners();
    }
  }

  /// تغيير الحد الأدنى لتنبيه المخزون
  Future<void> setMinStockAlert(int value) async {
    if (_minStockAlert != value && value > 0) {
      _minStockAlert = value;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('min_stock_alert', value);
      
      notifyListeners();
    }
  }

  /// تفعيل/إلغاء التحديث التلقائي
  Future<void> setAutoRefreshEnabled(bool enabled) async {
    if (_autoRefreshEnabled != enabled) {
      _autoRefreshEnabled = enabled;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('auto_refresh_enabled', enabled);
      
      notifyListeners();
    }
  }

  /// تغيير فترة التحديث التلقائي
  Future<void> setAutoRefreshInterval(int minutes) async {
    if (_autoRefreshInterval != minutes && minutes > 0) {
      _autoRefreshInterval = minutes;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('auto_refresh_interval', minutes);
      
      notifyListeners();
    }
  }

  /// إعادة تعيين الإعدادات للقيم الافتراضية
  Future<void> resetToDefaults() async {
    _currencySymbol = AppConstants.currencySymbol;
    _language = 'ar';
    _theme = 'light';
    _notificationsEnabled = true;
    _stockAlertsEnabled = true;
    _minStockAlert = AppConstants.defaultMinQuantity;
    _autoRefreshEnabled = true;
    _autoRefreshInterval = AppConstants.autoRefreshIntervalMinutes;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.currencyKey, _currencySymbol);
    await prefs.setString(AppConstants.languageKey, _language);
    await prefs.setString(AppConstants.themeKey, _theme);
    await prefs.setBool('notifications_enabled', _notificationsEnabled);
    await prefs.setBool('stock_alerts_enabled', _stockAlertsEnabled);
    await prefs.setInt('min_stock_alert', _minStockAlert);
    await prefs.setBool('auto_refresh_enabled', _autoRefreshEnabled);
    await prefs.setInt('auto_refresh_interval', _autoRefreshInterval);

    notifyListeners();
  }

  /// الحصول على اسم العملة
  String getCurrencyName() {
    return AppConstants.supportedCurrencies[_currencySymbol] ?? 'عملة غير معروفة';
  }

  /// التحقق من صحة رمز العملة
  bool isValidCurrency(String symbol) {
    return AppConstants.supportedCurrencies.containsKey(symbol);
  }

  /// تنسيق المبلغ مع رمز العملة
  String formatCurrency(double amount) {
    return '${amount.toStringAsFixed(AppConstants.decimalPlaces)} $_currencySymbol';
  }

  /// تصدير الإعدادات
  Map<String, dynamic> exportSettings() {
    return {
      'currency_symbol': _currencySymbol,
      'language': _language,
      'theme': _theme,
      'notifications_enabled': _notificationsEnabled,
      'stock_alerts_enabled': _stockAlertsEnabled,
      'min_stock_alert': _minStockAlert,
      'auto_refresh_enabled': _autoRefreshEnabled,
      'auto_refresh_interval': _autoRefreshInterval,
    };
  }

  /// استيراد الإعدادات
  Future<void> importSettings(Map<String, dynamic> settings) async {
    if (settings['currency_symbol'] != null && 
        isValidCurrency(settings['currency_symbol'])) {
      await setCurrencySymbol(settings['currency_symbol']);
    }
    
    if (settings['language'] != null) {
      await setLanguage(settings['language']);
    }
    
    if (settings['theme'] != null) {
      await setTheme(settings['theme']);
    }
    
    if (settings['notifications_enabled'] != null) {
      await setNotificationsEnabled(settings['notifications_enabled']);
    }
    
    if (settings['stock_alerts_enabled'] != null) {
      await setStockAlertsEnabled(settings['stock_alerts_enabled']);
    }
    
    if (settings['min_stock_alert'] != null) {
      await setMinStockAlert(settings['min_stock_alert']);
    }
    
    if (settings['auto_refresh_enabled'] != null) {
      await setAutoRefreshEnabled(settings['auto_refresh_enabled']);
    }
    
    if (settings['auto_refresh_interval'] != null) {
      await setAutoRefreshInterval(settings['auto_refresh_interval']);
    }
  }
}
