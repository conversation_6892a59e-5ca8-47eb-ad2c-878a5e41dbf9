import 'package:flutter/material.dart';
import '../constants/app_theme.dart';

/// ويدجت رقاقة التصفية
/// Filter Chip Widget for filtering options
class FilterChipWidget extends StatelessWidget {
  final String label;
  final bool isSelected;
  final ValueChanged<bool> onSelected;
  final Color? selectedColor;
  final Color? unselectedColor;

  const FilterChipWidget({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onSelected,
    this.selectedColor,
    this.unselectedColor,
  });

  @override
  Widget build(BuildContext context) {
    final selectedBgColor = selectedColor ?? AppTheme.primaryColor;
    final unselectedBgColor = unselectedColor ?? Colors.grey.shade200;

    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : AppTheme.textPrimary,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      onSelected: onSelected,
      backgroundColor: unselectedBgColor,
      selectedColor: selectedBgColor,
      checkmarkColor: Colors.white,
      elevation: isSelected ? 2 : 0,
      pressElevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: isSelected ? selectedBgColor : Colors.grey.shade300,
          width: 1,
        ),
      ),
    );
  }
}
