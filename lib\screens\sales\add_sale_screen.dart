import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../models/models.dart';
import '../../providers/providers.dart';

/// شاشة إضافة مبيعة جديدة
class AddSaleScreen extends StatefulWidget {
  static const String routeName = '/add_sale';

  const AddSaleScreen({super.key});

  @override
  State<AddSaleScreen> createState() => _AddSaleScreenState();
}

class _AddSaleScreenState extends State<AddSaleScreen> {
  final _formKey = GlobalKey<FormState>();
  final _invoiceController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _customerAddressController = TextEditingController();
  final _notesController = TextEditingController();
  final _discountController = TextEditingController(text: '0');

  DateTime _saleDate = DateTime.now();
  PaymentMethod _paymentMethod = PaymentMethod.cash;
  final List<SaleItem> _items = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _generateInvoiceNumber();
  }

  void _generateInvoiceNumber() {
    final now = DateTime.now();
    _invoiceController.text =
        'SAL-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${now.millisecondsSinceEpoch.toString().substring(8)}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة مبيعة جديدة'),
        actions: [
          IconButton(icon: const Icon(Icons.save), onPressed: _saveSale),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfo(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildCustomerInfo(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildItemsList(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildTotalSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات أساسية',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _invoiceController,
              decoration: const InputDecoration(
                labelText: 'رقم الفاتورة',
                prefixIcon: Icon(Icons.receipt),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'رقم الفاتورة مطلوب';
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ListTile(
              leading: const Icon(Icons.calendar_today),
              title: const Text('تاريخ البيع'),
              subtitle: Text(
                '${_saleDate.day}/${_saleDate.month}/${_saleDate.year}',
              ),
              trailing: const Icon(Icons.edit),
              onTap: _selectDate,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            DropdownButtonFormField<PaymentMethod>(
              value: _paymentMethod,
              decoration: const InputDecoration(
                labelText: 'طريقة الدفع',
                prefixIcon: Icon(Icons.payment),
              ),
              items: PaymentMethod.values.map((method) {
                return DropdownMenuItem(
                  value: method,
                  child: Text(method.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _paymentMethod = value!;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات العميل',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _customerNameController,
              decoration: const InputDecoration(
                labelText: 'اسم العميل (اختياري)',
                prefixIcon: Icon(Icons.person),
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _customerPhoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف (اختياري)',
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _customerAddressController,
              decoration: const InputDecoration(
                labelText: 'العنوان (اختياري)',
                prefixIcon: Icon(Icons.location_on),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsList() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'عناصر البيع',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                ElevatedButton.icon(
                  onPressed: _addItem,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة عنصر'),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            if (_items.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(AppConstants.largePadding),
                  child: Text('لم يتم إضافة أي عناصر بعد'),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _items.length,
                itemBuilder: (context, index) {
                  final item = _items[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      title: Text(item.productName),
                      subtitle: Text(
                        'الكمية: ${item.quantity} - السعر: ${item.unitPrice}',
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '${item.totalPrice}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          IconButton(
                            icon: const Icon(
                              Icons.delete,
                              color: AppTheme.errorColor,
                            ),
                            onPressed: () => _removeItem(index),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalSection() {
    final subtotal = _items.fold<double>(
      0,
      (sum, item) => sum + item.totalPrice,
    );
    final discount = double.tryParse(_discountController.text) ?? 0;
    final total = subtotal - discount;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('المجموع الفرعي:'),
                Text('$subtotal ${AppConstants.currencySymbol}'),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Expanded(child: Text('الخصم:')),
                SizedBox(
                  width: 100,
                  child: TextFormField(
                    controller: _discountController,
                    decoration: const InputDecoration(
                      suffix: Text(AppConstants.currencySymbol),
                      isDense: true,
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) => setState(() {}),
                  ),
                ),
              ],
            ),
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الإجمالي:',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(
                  '$total ${AppConstants.currencySymbol}',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات',
                prefixIcon: Icon(Icons.note),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveSale,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: _isLoading
            ? const CircularProgressIndicator()
            : const Text('حفظ المبيعة'),
      ),
    );
  }

  void _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _saleDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        _saleDate = date;
      });
    }
  }

  void _addItem() {
    // هنا يمكن إضافة شاشة اختيار المنتج
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة شاشة اختيار المنتج قريباً'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  void _removeItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  void _saveSale() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب إضافة عنصر واحد على الأقل'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final saleProvider = Provider.of<SaleProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      final subtotal = _items.fold<double>(
        0,
        (sum, item) => sum + item.totalPrice,
      );
      final discount = double.tryParse(_discountController.text) ?? 0;
      final total = subtotal - discount;

      final sale = Sale(
        id: 0,
        invoiceNumber: _invoiceController.text,
        customerName: _customerNameController.text.isEmpty
            ? null
            : _customerNameController.text,
        customerPhone: _customerPhoneController.text.isEmpty
            ? null
            : _customerPhoneController.text,
        customerAddress: _customerAddressController.text.isEmpty
            ? null
            : _customerAddressController.text,
        totalAmount: total,
        paidAmount: total, // افتراض أن المبلغ مدفوع بالكامل
        remainingAmount: 0,
        discountAmount: discount,
        status: SaleStatus.completed,
        paymentMethod: _paymentMethod,
        saleDate: _saleDate,
        notes: _notesController.text,
        userId: authProvider.currentUser!.id ?? 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final success = await saleProvider.createSale(sale, _items);

      if (success && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ المبيعة بنجاح'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(saleProvider.errorMessage ?? 'فشل في حفظ المبيعة'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _invoiceController.dispose();
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _customerAddressController.dispose();
    _notesController.dispose();
    _discountController.dispose();
    super.dispose();
  }
}
