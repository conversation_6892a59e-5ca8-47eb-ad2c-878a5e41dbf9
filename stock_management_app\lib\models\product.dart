/// نموذج المنتج
/// Product Model for inventory management
class Product {
  final int? id;
  final String name;
  final String? description;
  final String? barcode;
  final int categoryId;
  final double purchasePrice;
  final double sellingPrice;
  final int quantity;
  final int minQuantity; // الحد الأدنى للكمية للتنبيه
  final String? imagePath;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Product({
    this.id,
    required this.name,
    this.description,
    this.barcode,
    required this.categoryId,
    required this.purchasePrice,
    required this.sellingPrice,
    required this.quantity,
    this.minQuantity = 5,
    this.imagePath,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  /// تحويل من Map إلى Product object
  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      barcode: map['barcode'],
      categoryId: map['category_id'],
      purchasePrice: map['purchase_price'].toDouble(),
      sellingPrice: map['selling_price'].toDouble(),
      quantity: map['quantity'],
      minQuantity: map['min_quantity'] ?? 5,
      imagePath: map['image_path'],
      isActive: map['is_active'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at']) 
          : null,
    );
  }

  /// تحويل من Product object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'barcode': barcode,
      'category_id': categoryId,
      'purchase_price': purchasePrice,
      'selling_price': sellingPrice,
      'quantity': quantity,
      'min_quantity': minQuantity,
      'image_path': imagePath,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من المنتج
  Product copyWith({
    int? id,
    String? name,
    String? description,
    String? barcode,
    int? categoryId,
    double? purchasePrice,
    double? sellingPrice,
    int? quantity,
    int? minQuantity,
    String? imagePath,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      barcode: barcode ?? this.barcode,
      categoryId: categoryId ?? this.categoryId,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      quantity: quantity ?? this.quantity,
      minQuantity: minQuantity ?? this.minQuantity,
      imagePath: imagePath ?? this.imagePath,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// حساب هامش الربح
  double get profitMargin {
    if (purchasePrice == 0) return 0;
    return ((sellingPrice - purchasePrice) / purchasePrice) * 100;
  }

  /// حساب الربح المتوقع
  double get expectedProfit {
    return (sellingPrice - purchasePrice) * quantity;
  }

  /// التحقق من انخفاض الكمية
  bool get isLowStock {
    return quantity <= minQuantity;
  }

  /// التحقق من نفاد المخزون
  bool get isOutOfStock {
    return quantity == 0;
  }

  /// حالة المخزون
  StockStatus get stockStatus {
    if (isOutOfStock) return StockStatus.outOfStock;
    if (isLowStock) return StockStatus.lowStock;
    return StockStatus.inStock;
  }

  @override
  String toString() {
    return 'Product{id: $id, name: $name, quantity: $quantity, price: $sellingPrice}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Product &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name;

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}

/// حالة المخزون
enum StockStatus {
  inStock,    // متوفر
  lowStock,   // كمية قليلة
  outOfStock, // نفد المخزون
}

extension StockStatusExtension on StockStatus {
  String get displayName {
    switch (this) {
      case StockStatus.inStock:
        return 'متوفر';
      case StockStatus.lowStock:
        return 'كمية قليلة';
      case StockStatus.outOfStock:
        return 'نفد المخزون';
    }
  }

  String get color {
    switch (this) {
      case StockStatus.inStock:
        return '#4CAF50'; // أخضر
      case StockStatus.lowStock:
        return '#FF9800'; // برتقالي
      case StockStatus.outOfStock:
        return '#F44336'; // أحمر
    }
  }
}
