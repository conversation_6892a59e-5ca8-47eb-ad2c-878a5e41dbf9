{"recommendations": ["dart-code.dart-code", "dart-code.flutter", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-eslint", "esbenp.prettier-vscode", "gruntfuggly.todo-tree", "alefragnani.bookmarks", "ms-vscode.vscode-json", "ms-vscode.hexeditor", "ms-vscode.vscode-markdown", "yzhang.markdown-all-in-one", "davidanson.vscode-markdownlint", "ms-vscode.vscode-github-issue-notebooks", "github.vscode-pull-request-github", "ms-vscode.remote-repositories", "ms-vscode.remote-ssh", "ms-vscode.remote-ssh-edit", "ms-vscode.remote-explorer", "ms-vscode.vscode-remote-extensionpack", "ms-vscode-remote.remote-containers", "ms-vscode-remote.remote-wsl", "ms-vscode.powershell", "ms-vscode.vscode-terminal-tabs", "ms-vscode.vscode-speech", "ms-vscode.live-server", "ritwickdey.liveserver", "ms-vscode.vscode-github-issue-notebooks", "github.copilot", "github.copilot-chat", "ms-vscode.vscode-ai-toolkit", "ms-toolsai.jupyter", "ms-python.python", "ms-python.vscode-pylance", "ms-python.flake8", "ms-python.black-formatter", "ms-python.isort", "ms-python.pylint", "ms-python.mypy-type-checker"], "unwantedRecommendations": ["ms-vscode.vscode-typescript", "hookyqr.beautify", "dbaeumer.vscode-eslint"]}