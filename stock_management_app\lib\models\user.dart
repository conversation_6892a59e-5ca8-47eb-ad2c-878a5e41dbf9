/// نموذج المستخدم
/// User Model for authentication and authorization
class User {
  final int? id;
  final String username;
  final String password;
  final String fullName;
  final UserRole role;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastLogin;

  User({
    this.id,
    required this.username,
    required this.password,
    required this.fullName,
    required this.role,
    this.isActive = true,
    required this.createdAt,
    this.lastLogin,
  });

  /// تحويل من Map إلى User object
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'],
      username: map['username'],
      password: map['password'],
      fullName: map['full_name'],
      role: UserRole.values[map['role']],
      isActive: map['is_active'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      lastLogin: map['last_login'] != null 
          ? DateTime.parse(map['last_login']) 
          : null,
    );
  }

  /// تحويل من User object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'username': username,
      'password': password,
      'full_name': fullName,
      'role': role.index,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'last_login': lastLogin?.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من المستخدم
  User copyWith({
    int? id,
    String? username,
    String? password,
    String? fullName,
    UserRole? role,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLogin,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      password: password ?? this.password,
      fullName: fullName ?? this.fullName,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }

  @override
  String toString() {
    return 'User{id: $id, username: $username, fullName: $fullName, role: $role}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          username == other.username;

  @override
  int get hashCode => id.hashCode ^ username.hashCode;
}

/// أدوار المستخدمين
/// User roles for authorization
enum UserRole {
  admin,    // مدير - صلاحيات كاملة
  employee, // موظف - صلاحيات محدودة
}

extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.admin:
        return 'مدير';
      case UserRole.employee:
        return 'موظف';
    }
  }

  /// التحقق من صلاحية إضافة منتج
  bool get canAddProduct {
    switch (this) {
      case UserRole.admin:
        return true;
      case UserRole.employee:
        return false;
    }
  }

  /// التحقق من صلاحية تعديل منتج
  bool get canEditProduct {
    switch (this) {
      case UserRole.admin:
        return true;
      case UserRole.employee:
        return false;
    }
  }

  /// التحقق من صلاحية حذف منتج
  bool get canDeleteProduct {
    switch (this) {
      case UserRole.admin:
        return true;
      case UserRole.employee:
        return false;
    }
  }

  /// التحقق من صلاحية عرض التقارير
  bool get canViewReports {
    switch (this) {
      case UserRole.admin:
        return true;
      case UserRole.employee:
        return true;
    }
  }

  /// التحقق من صلاحية إدارة المستخدمين
  bool get canManageUsers {
    switch (this) {
      case UserRole.admin:
        return true;
      case UserRole.employee:
        return false;
    }
  }
}
