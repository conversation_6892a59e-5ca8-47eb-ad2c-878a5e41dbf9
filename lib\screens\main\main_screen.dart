import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/product_provider.dart';
import '../auth/simple_login_screen.dart';
import '../categories/categories_screen.dart';
import '../home/<USER>';
import '../products/products_screen.dart';
import '../reports/reports_screen.dart';
import '../settings/settings_screen.dart';

/// الشاشة الرئيسية مع شريط التنقل السفلي
/// Main Screen with Bottom Navigation
class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    // تحميل البيانات عند بدء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  Future<void> _loadInitialData() async {
    if (!mounted) return;

    try {
      // تحميل الفئات
      final categoryProvider = Provider.of<CategoryProvider>(
        context,
        listen: false,
      );

      // تحميل المنتجات
      final productProvider = Provider.of<ProductProvider>(
        context,
        listen: false,
      );

      // تحميل البيانات بشكل متوازي
      await Future.wait([
        categoryProvider.loadCategories(),
        productProvider.loadProducts(),
      ]);
    } catch (e) {
      debugPrint('Error loading initial data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;
        final isAdmin = user?.role.name == 'admin';

        // قائمة الشاشات
        final List<Widget> screens = [
          const DashboardScreen(), // الرئيسية
          const ProductsScreen(), // المنتجات
          const CategoriesScreen(), // الفئات
          if (isAdmin) const ReportsScreen(), // التقارير (للمدير فقط)
          const SettingsScreen(), // الإعدادات
        ];

        // قائمة عناصر التنقل
        final List<BottomNavigationBarItem> navItems = [
          const BottomNavigationBarItem(
            icon: Icon(Icons.dashboard_outlined),
            activeIcon: Icon(Icons.dashboard),
            label: 'الرئيسية',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.inventory_2_outlined),
            activeIcon: Icon(Icons.inventory_2),
            label: 'المنتجات',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.category_outlined),
            activeIcon: Icon(Icons.category),
            label: 'الفئات',
          ),
          if (isAdmin)
            const BottomNavigationBarItem(
              icon: Icon(Icons.analytics_outlined),
              activeIcon: Icon(Icons.analytics),
              label: 'التقارير',
            ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.settings_outlined),
            activeIcon: Icon(Icons.settings),
            label: 'الإعدادات',
          ),
        ];

        return Scaffold(
          appBar: AppBar(
            title: Text(
              _getAppBarTitle(),
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
            ),
            backgroundColor: Colors.blue.shade600,
            foregroundColor: Colors.white,
            elevation: 0,
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue.shade600, Colors.blue.shade800],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
            actions: [
              // معلومات المستخدم
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: CircleAvatar(
                        radius: 16,
                        backgroundColor: Colors.white,
                        child: Icon(
                          isAdmin ? Icons.admin_panel_settings : Icons.person,
                          color: Colors.blue.shade600,
                          size: 18,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user?.username ?? 'مستخدم',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          isAdmin ? 'مدير' : 'موظف',
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.white70,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // زر تسجيل الخروج
              IconButton(
                icon: const Icon(Icons.logout_outlined),
                onPressed: () => _showLogoutDialog(),
                tooltip: 'تسجيل الخروج',
              ),
            ],
          ),
          body: IndexedStack(
            index: _currentIndex,
            children: screens.map((screen) {
              // تمرير callback للتنقل إذا كانت الشاشة تحتاجه
              if (screen is DashboardScreen) {
                return DashboardScreen(
                  onNavigateToTab: (index) {
                    setState(() {
                      _currentIndex = index;
                    });
                  },
                );
              }
              return screen;
            }).toList(),
          ),
          bottomNavigationBar: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: BottomNavigationBar(
              currentIndex: _currentIndex,
              onTap: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              type: BottomNavigationBarType.fixed,
              backgroundColor: Colors.white,
              selectedItemColor: Colors.blue.shade600,
              unselectedItemColor: Colors.grey.shade500,
              selectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 11,
              ),
              elevation: 0,
              items: navItems,
            ),
          ),
        );
      },
    );
  }

  String _getAppBarTitle() {
    final titles = [
      AppConstants.appName,
      'إدارة المنتجات',
      'إدارة الفئات',
      'التقارير',
      'الإعدادات',
    ];

    // تعديل الفهرس للمدير (لديه شاشة التقارير)
    final user = Provider.of<AuthProvider>(context, listen: false).currentUser;
    final isAdmin = user?.role.name == 'admin';

    if (!isAdmin && _currentIndex >= 3) {
      return titles[_currentIndex + 1]; // تخطي شاشة التقارير
    }

    return titles[_currentIndex];
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Provider.of<AuthProvider>(context, listen: false).logout();
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const SimpleLoginScreen(),
                ),
              );
            },
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
