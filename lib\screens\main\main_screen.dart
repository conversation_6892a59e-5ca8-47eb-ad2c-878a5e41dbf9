import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../auth/simple_login_screen.dart';
import '../categories/categories_screen.dart';
import '../home/<USER>';
import '../products/products_screen.dart';
import '../reports/reports_screen.dart';
import '../settings/settings_screen.dart';

/// الشاشة الرئيسية مع شريط التنقل السفلي
/// Main Screen with Bottom Navigation
class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;
        final isAdmin = user?.role.name == 'admin';

        // قائمة الشاشات
        final List<Widget> screens = [
          const DashboardScreen(), // الرئيسية
          const ProductsScreen(), // المنتجات
          const CategoriesScreen(), // الفئات
          if (isAdmin) const ReportsScreen(), // التقارير (للمدير فقط)
          const SettingsScreen(), // الإعدادات
        ];

        // قائمة عناصر التنقل
        final List<BottomNavigationBarItem> navItems = [
          const BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'الرئيسية',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.inventory_2),
            label: 'المنتجات',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.category),
            label: 'الفئات',
          ),
          if (isAdmin)
            const BottomNavigationBarItem(
              icon: Icon(Icons.analytics),
              label: 'التقارير',
            ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'الإعدادات',
          ),
        ];

        return Scaffold(
          appBar: AppBar(
            title: Text(_getAppBarTitle()),
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            actions: [
              // معلومات المستخدم
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.white,
                      child: Icon(
                        isAdmin ? Icons.admin_panel_settings : Icons.person,
                        color: Colors.blue,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user?.username ?? 'مستخدم',
                          style: const TextStyle(fontSize: 12),
                        ),
                        Text(
                          isAdmin ? 'مدير' : 'موظف',
                          style: const TextStyle(fontSize: 10),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // زر تسجيل الخروج
              IconButton(
                icon: const Icon(Icons.logout),
                onPressed: () => _showLogoutDialog(),
              ),
            ],
          ),
          body: IndexedStack(
            index: _currentIndex,
            children: screens.map((screen) {
              // تمرير callback للتنقل إذا كانت الشاشة تحتاجه
              if (screen is DashboardScreen) {
                return DashboardScreen(
                  onNavigateToTab: (index) {
                    setState(() {
                      _currentIndex = index;
                    });
                  },
                );
              }
              return screen;
            }).toList(),
          ),
          bottomNavigationBar: BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            type: BottomNavigationBarType.fixed,
            selectedItemColor: Colors.blue,
            unselectedItemColor: Colors.grey,
            items: navItems,
          ),
        );
      },
    );
  }

  String _getAppBarTitle() {
    final titles = [
      AppConstants.appName,
      'إدارة المنتجات',
      'إدارة الفئات',
      'التقارير',
      'الإعدادات',
    ];

    // تعديل الفهرس للمدير (لديه شاشة التقارير)
    final user = Provider.of<AuthProvider>(context, listen: false).currentUser;
    final isAdmin = user?.role.name == 'admin';

    if (!isAdmin && _currentIndex >= 3) {
      return titles[_currentIndex + 1]; // تخطي شاشة التقارير
    }

    return titles[_currentIndex];
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Provider.of<AuthProvider>(context, listen: false).logout();
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const SimpleLoginScreen(),
                ),
              );
            },
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
