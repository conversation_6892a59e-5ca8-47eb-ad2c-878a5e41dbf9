import 'package:sqflite/sqflite.dart';

import '../constants/app_constants.dart';
import '../database/database_helper.dart';
import '../models/models.dart';

/// خدمة إدارة الصلاحيات
/// Permission Service for managing user permissions
class PermissionService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع الصلاحيات
  Future<List<Permission>> getAllPermissions() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.permissionsTable,
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'category, display_name',
    );

    return List.generate(maps.length, (i) {
      return Permission.fromMap(maps[i]);
    });
  }

  /// الحصول على صلاحيات المستخدم
  Future<List<Permission>> getUserPermissions(int userId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT p.* FROM ${AppConstants.permissionsTable} p
      INNER JOIN ${AppConstants.userPermissionsTable} up ON p.id = up.permission_id
      WHERE up.user_id = ? AND up.is_granted = 1 AND p.is_active = 1
      ORDER BY p.category, p.display_name
    ''',
      [userId],
    );

    return List.generate(maps.length, (i) {
      return Permission.fromMap(maps[i]);
    });
  }

  /// التحقق من صلاحية المستخدم
  Future<bool> hasPermission(int userId, String permissionName) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      '''
      SELECT COUNT(*) as count FROM ${AppConstants.permissionsTable} p
      INNER JOIN ${AppConstants.userPermissionsTable} up ON p.id = up.permission_id
      WHERE up.user_id = ? AND p.name = ? AND up.is_granted = 1 AND p.is_active = 1
    ''',
      [userId, permissionName],
    );

    return (result.first['count'] as int) > 0;
  }

  /// منح صلاحية للمستخدم
  Future<bool> grantPermission(int userId, int permissionId) async {
    final db = await _databaseHelper.database;

    try {
      await db.insert(
        AppConstants.userPermissionsTable,
        {
          'user_id': userId,
          'permission_id': permissionId,
          'is_granted': 1,
          'created_at': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return true;
    } catch (e) {
      print('Error granting permission: $e');
      return false;
    }
  }

  /// إلغاء صلاحية المستخدم
  Future<bool> revokePermission(int userId, int permissionId) async {
    final db = await _databaseHelper.database;

    try {
      await db.update(
        AppConstants.userPermissionsTable,
        {'is_granted': 0, 'updated_at': DateTime.now().toIso8601String()},
        where: 'user_id = ? AND permission_id = ?',
        whereArgs: [userId, permissionId],
      );
      return true;
    } catch (e) {
      print('Error revoking permission: $e');
      return false;
    }
  }

  /// تحديث صلاحيات المستخدم
  Future<bool> updateUserPermissions(
    int userId,
    List<int> permissionIds,
  ) async {
    final db = await _databaseHelper.database;

    try {
      await db.transaction((txn) async {
        // حذف جميع صلاحيات المستخدم الحالية
        await txn.delete(
          AppConstants.userPermissionsTable,
          where: 'user_id = ?',
          whereArgs: [userId],
        );

        // إضافة الصلاحيات الجديدة
        for (final permissionId in permissionIds) {
          await txn.insert(AppConstants.userPermissionsTable, {
            'user_id': userId,
            'permission_id': permissionId,
            'is_granted': 1,
            'created_at': DateTime.now().toIso8601String(),
          });
        }
      });
      return true;
    } catch (e) {
      print('Error updating user permissions: $e');
      return false;
    }
  }

  /// الحصول على الصلاحيات حسب الفئة
  Future<Map<String, List<Permission>>> getPermissionsByCategory() async {
    final permissions = await getAllPermissions();
    final Map<String, List<Permission>> categorizedPermissions = {};

    for (final permission in permissions) {
      if (!categorizedPermissions.containsKey(permission.category)) {
        categorizedPermissions[permission.category] = [];
      }
      categorizedPermissions[permission.category]!.add(permission);
    }

    return categorizedPermissions;
  }

  /// الحصول على صلاحيات المستخدم مع حالة المنح
  Future<Map<int, bool>> getUserPermissionStatus(int userId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.userPermissionsTable,
      where: 'user_id = ?',
      whereArgs: [userId],
    );

    final Map<int, bool> permissionStatus = {};
    for (final map in maps) {
      permissionStatus[map['permission_id']] = map['is_granted'] == 1;
    }

    return permissionStatus;
  }

  /// إنشاء صلاحية جديدة
  Future<int?> createPermission(Permission permission) async {
    final db = await _databaseHelper.database;

    try {
      return await db.insert(AppConstants.permissionsTable, permission.toMap());
    } catch (e) {
      print('Error creating permission: $e');
      return null;
    }
  }

  /// تحديث صلاحية
  Future<bool> updatePermission(Permission permission) async {
    final db = await _databaseHelper.database;

    try {
      await db.update(
        AppConstants.permissionsTable,
        permission.copyWith(updatedAt: DateTime.now()).toMap(),
        where: 'id = ?',
        whereArgs: [permission.id],
      );
      return true;
    } catch (e) {
      print('Error updating permission: $e');
      return false;
    }
  }

  /// حذف صلاحية (تعطيل)
  Future<bool> deletePermission(int permissionId) async {
    final db = await _databaseHelper.database;

    try {
      await db.update(
        AppConstants.permissionsTable,
        {'is_active': 0, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [permissionId],
      );
      return true;
    } catch (e) {
      print('Error deleting permission: $e');
      return false;
    }
  }

  /// نسخ صلاحيات من مستخدم إلى آخر
  Future<bool> copyPermissions(int fromUserId, int toUserId) async {
    final db = await _databaseHelper.database;

    try {
      // الحصول على صلاحيات المستخدم المصدر
      final sourcePermissions = await db.query(
        AppConstants.userPermissionsTable,
        where: 'user_id = ? AND is_granted = 1',
        whereArgs: [fromUserId],
      );

      await db.transaction((txn) async {
        // حذف صلاحيات المستخدم الهدف
        await txn.delete(
          AppConstants.userPermissionsTable,
          where: 'user_id = ?',
          whereArgs: [toUserId],
        );

        // نسخ الصلاحيات
        for (final permission in sourcePermissions) {
          await txn.insert(AppConstants.userPermissionsTable, {
            'user_id': toUserId,
            'permission_id': permission['permission_id'],
            'is_granted': 1,
            'created_at': DateTime.now().toIso8601String(),
          });
        }
      });

      return true;
    } catch (e) {
      print('Error copying permissions: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات الصلاحيات
  Future<Map<String, dynamic>> getPermissionStats() async {
    final db = await _databaseHelper.database;

    // إجمالي الصلاحيات
    final totalPermissions = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.permissionsTable}
      WHERE is_active = 1
    ''');

    // الصلاحيات حسب الفئة
    final permissionsByCategory = await db.rawQuery('''
      SELECT category, COUNT(*) as count FROM ${AppConstants.permissionsTable}
      WHERE is_active = 1
      GROUP BY category
    ''');

    // المستخدمين الذين لديهم صلاحيات
    final usersWithPermissions = await db.rawQuery('''
      SELECT COUNT(DISTINCT user_id) as count FROM ${AppConstants.userPermissionsTable}
      WHERE is_granted = 1
    ''');

    return {
      'total_permissions': totalPermissions.first['count'],
      'permissions_by_category': permissionsByCategory,
      'users_with_permissions': usersWithPermissions.first['count'],
    };
  }

  /// البحث في الصلاحيات
  Future<List<Permission>> searchPermissions(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.permissionsTable,
      where: '''
        is_active = 1 AND (
          name LIKE ? OR 
          display_name LIKE ? OR 
          description LIKE ? OR
          category LIKE ?
        )
      ''',
      whereArgs: ['%$query%', '%$query%', '%$query%', '%$query%'],
      orderBy: 'category, display_name',
    );

    return List.generate(maps.length, (i) {
      return Permission.fromMap(maps[i]);
    });
  }
}
