# دليل المساهمة - Contributing Guide

نرحب بمساهماتكم في تطوير تطبيق إدارة المخازن! هذا الدليل سيساعدكم على فهم كيفية المساهمة بفعالية.

## 🤝 كيفية المساهمة

### 1. الإبلاغ عن الأخطاء (Bug Reports)

عند العثور على خطأ، يرجى:
- التأكد من أن الخطأ لم يتم الإبلاغ عنه مسبقاً
- إنشاء issue جديد مع التفاصيل التالية:
  - وصف واضح للخطأ
  - خطوات إعادة إنتاج الخطأ
  - السلوك المتوقع مقابل السلوك الفعلي
  - لقطات شاشة إن أمكن
  - معلومات البيئة (نظام التشغيل، إصدار Flutter، إلخ)

### 2. اقتراح المميزات (Feature Requests)

لاقتراح ميزة جديدة:
- تحقق من عدم وجود اقتراح مشابه
- اشرح الحاجة للميزة والفائدة المتوقعة
- قدم أمثلة أو mockups إن أمكن
- ناقش التنفيذ المقترح

### 3. المساهمة بالكود

#### متطلبات البيئة
- Flutter SDK 3.16.0 أو أحدث
- Dart SDK 3.2.0 أو أحدث
- Android Studio أو VS Code
- Git

#### خطوات المساهمة

1. **Fork المستودع**
   ```bash
   git clone https://github.com/your-username/stock-management-app.git
   cd stock-management-app
   ```

2. **إنشاء فرع جديد**
   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **تثبيت التبعيات**
   ```bash
   flutter pub get
   ```

4. **إجراء التغييرات**
   - اتبع معايير الكود المحددة
   - أضف اختبارات للمميزات الجديدة
   - تأكد من عمل جميع الاختبارات

5. **اختبار التغييرات**
   ```bash
   flutter test
   flutter analyze
   dart format .
   ```

6. **Commit التغييرات**
   ```bash
   git add .
   git commit -m "feat: add amazing feature"
   ```

7. **Push للفرع**
   ```bash
   git push origin feature/amazing-feature
   ```

8. **إنشاء Pull Request**

## 📝 معايير الكود

### تنسيق الكود
- استخدم `dart format` لتنسيق الكود
- اتبع [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- استخدم أسماء متغيرات واضحة ومعبرة

### التعليقات
- اكتب تعليقات باللغة العربية للوظائف الرئيسية
- استخدم التعليقات الإنجليزية للكود التقني
- وثق المعاملات والقيم المرجعة

### البنية
- اتبع بنية المجلدات الحالية
- ضع الملفات في المجلدات المناسبة
- استخدم أسماء ملفات واضحة

### الاختبارات
- اكتب اختبارات للمميزات الجديدة
- تأكد من تغطية الكود بنسبة لا تقل عن 80%
- اختبر الحالات الحدية والأخطاء

## 🏷️ تسمية Commits

استخدم التنسيق التالي للـ commits:

```
type(scope): description

[optional body]

[optional footer]
```

### أنواع Commits
- `feat`: ميزة جديدة
- `fix`: إصلاح خطأ
- `docs`: تحديث التوثيق
- `style`: تغييرات التنسيق
- `refactor`: إعادة هيكلة الكود
- `test`: إضافة أو تحديث الاختبارات
- `chore`: مهام الصيانة

### أمثلة
```bash
feat(auth): add biometric authentication
fix(database): resolve connection timeout issue
docs(readme): update installation instructions
style(login): improve button styling
refactor(providers): simplify state management
test(products): add unit tests for product service
chore(deps): update flutter dependencies
```

## 🔍 مراجعة الكود

### معايير المراجعة
- وضوح الكود وقابليته للقراءة
- اتباع معايير المشروع
- وجود اختبارات مناسبة
- عدم كسر الوظائف الموجودة
- الأداء والأمان

### عملية المراجعة
1. سيتم مراجعة جميع Pull Requests
2. قد نطلب تعديلات أو توضيحات
3. بعد الموافقة، سيتم دمج التغييرات
4. سيتم إشعاركم بحالة المراجعة

## 🐛 الإبلاغ عن مشاكل الأمان

للإبلاغ عن مشاكل الأمان:
- لا تنشر المشكلة علناً
- أرسل بريد إلكتروني إلى: <EMAIL>
- اشرح المشكلة بالتفصيل
- قدم خطوات إعادة الإنتاج إن أمكن

## 📋 قائمة التحقق للـ Pull Request

قبل إرسال Pull Request، تأكد من:

- [ ] الكود يتبع معايير المشروع
- [ ] جميع الاختبارات تعمل بنجاح
- [ ] تم إضافة اختبارات للمميزات الجديدة
- [ ] التوثيق محدث إن لزم الأمر
- [ ] لا توجد تحذيرات من المحلل
- [ ] الكود منسق بشكل صحيح
- [ ] رسالة Commit واضحة ومفيدة
- [ ] تم اختبار التغييرات على منصات متعددة

## 🎯 أولويات التطوير

### أولوية عالية
- إصلاح الأخطاء الحرجة
- تحسينات الأمان
- تحسينات الأداء
- إمكانية الوصول

### أولوية متوسطة
- مميزات جديدة مطلوبة
- تحسينات واجهة المستخدم
- تحسينات تجربة المستخدم
- التوثيق

### أولوية منخفضة
- تحسينات الكود
- مميزات إضافية
- تحسينات التطوير

## 🌍 الترجمة والتوطين

للمساهمة في الترجمة:
- أضف ملفات الترجمة في `lib/l10n/`
- اتبع تنسيق ARB المعياري
- تأكد من دقة الترجمة
- اختبر الترجمة في السياق

## 📚 الموارد المفيدة

- [Flutter Documentation](https://docs.flutter.dev/)
- [Dart Language Guide](https://dart.dev/guides)
- [Material Design Guidelines](https://material.io/design)
- [Git Best Practices](https://git-scm.com/book)

## 🤔 الحصول على المساعدة

إذا كنت بحاجة للمساعدة:
- اقرأ التوثيق أولاً
- ابحث في Issues الموجودة
- انضم إلى مناقشات المجتمع
- اطرح سؤالاً في issue جديد

## 🙏 شكر وتقدير

نشكر جميع المساهمين الذين يساعدون في تحسين هذا المشروع. مساهماتكم تجعل التطبيق أفضل للجميع!

---

**ملاحظة**: هذا المشروع يتبع [Contributor Covenant Code of Conduct](CODE_OF_CONDUCT.md). بالمشاركة، أنت توافق على الالتزام بهذه المعايير.
