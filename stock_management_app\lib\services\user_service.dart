import '../database/database_helper.dart';
import '../models/models.dart';
import '../constants/app_constants.dart';

/// خدمة المستخدمين
/// User Service for user management operations
class UserService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// تسجيل دخول المستخدم
  Future<User?> login(String username, String password) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.usersTable,
      where: 'username = ? AND password = ? AND is_active = 1',
      whereArgs: [username, password],
    );

    if (maps.isNotEmpty) {
      final user = User.fromMap(maps.first);
      
      // تحديث وقت آخر دخول
      await updateLastLogin(user.id!);
      
      return user.copyWith(lastLogin: DateTime.now());
    }
    
    return null;
  }

  /// إنشاء مستخدم جديد
  Future<int> createUser(User user) async {
    final db = await _databaseHelper.database;
    
    // التحقق من عدم وجود اسم المستخدم مسبقاً
    final existingUser = await getUserByUsername(user.username);
    if (existingUser != null) {
      throw Exception('اسم المستخدم موجود مسبقاً');
    }
    
    return await db.insert(AppConstants.usersTable, user.toMap());
  }

  /// الحصول على مستخدم بواسطة المعرف
  Future<User?> getUserById(int id) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.usersTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    
    return null;
  }

  /// الحصول على مستخدم بواسطة اسم المستخدم
  Future<User?> getUserByUsername(String username) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.usersTable,
      where: 'username = ?',
      whereArgs: [username],
    );

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    
    return null;
  }

  /// الحصول على جميع المستخدمين
  Future<List<User>> getAllUsers() async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.usersTable,
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return User.fromMap(maps[i]);
    });
  }

  /// الحصول على المستخدمين النشطين
  Future<List<User>> getActiveUsers() async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.usersTable,
      where: 'is_active = 1',
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return User.fromMap(maps[i]);
    });
  }

  /// تحديث بيانات المستخدم
  Future<int> updateUser(User user) async {
    final db = await _databaseHelper.database;
    
    return await db.update(
      AppConstants.usersTable,
      user.toMap(),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  /// تحديث كلمة مرور المستخدم
  Future<int> updatePassword(int userId, String newPassword) async {
    final db = await _databaseHelper.database;
    
    return await db.update(
      AppConstants.usersTable,
      {'password': newPassword},
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  /// تحديث وقت آخر دخول
  Future<int> updateLastLogin(int userId) async {
    final db = await _databaseHelper.database;
    
    return await db.update(
      AppConstants.usersTable,
      {'last_login': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  /// تفعيل/إلغاء تفعيل المستخدم
  Future<int> toggleUserStatus(int userId, bool isActive) async {
    final db = await _databaseHelper.database;
    
    return await db.update(
      AppConstants.usersTable,
      {'is_active': isActive ? 1 : 0},
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  /// حذف المستخدم
  Future<int> deleteUser(int userId) async {
    final db = await _databaseHelper.database;
    
    // التحقق من عدم وجود عمليات مرتبطة بالمستخدم
    final transactionCount = await _getTransactionCountByUser(userId);
    if (transactionCount > 0) {
      throw Exception('لا يمكن حذف المستخدم لوجود عمليات مرتبطة به');
    }
    
    return await db.delete(
      AppConstants.usersTable,
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  /// البحث في المستخدمين
  Future<List<User>> searchUsers(String query) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.usersTable,
      where: 'username LIKE ? OR full_name LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return User.fromMap(maps[i]);
    });
  }

  /// الحصول على عدد المستخدمين حسب الدور
  Future<Map<UserRole, int>> getUserCountByRole() async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT role, COUNT(*) as count 
      FROM ${AppConstants.usersTable} 
      WHERE is_active = 1 
      GROUP BY role
    ''');

    Map<UserRole, int> result = {};
    for (var map in maps) {
      UserRole role = UserRole.values[map['role']];
      result[role] = map['count'];
    }
    
    return result;
  }

  /// التحقق من صحة كلمة المرور
  bool validatePassword(String password) {
    // كلمة المرور يجب أن تكون على الأقل 6 أحرف
    return password.length >= 6;
  }

  /// التحقق من صحة اسم المستخدم
  bool validateUsername(String username) {
    // اسم المستخدم يجب أن يكون على الأقل 3 أحرف ولا يحتوي على مسافات
    return username.length >= 3 && !username.contains(' ');
  }

  /// الحصول على عدد العمليات للمستخدم
  Future<int> _getTransactionCountByUser(int userId) async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT COUNT(*) as count 
      FROM ${AppConstants.transactionLogsTable} 
      WHERE user_id = ?
    ''', [userId]);
    
    return result.first['count'] as int;
  }

  /// إنشاء مستخدم مدير افتراضي
  Future<void> createDefaultAdmin() async {
    final existingAdmin = await getUserByUsername('admin');
    if (existingAdmin == null) {
      final admin = User(
        username: 'admin',
        password: 'admin123',
        fullName: 'مدير النظام',
        role: UserRole.admin,
        createdAt: DateTime.now(),
      );
      
      await createUser(admin);
    }
  }

  /// تغيير دور المستخدم
  Future<int> changeUserRole(int userId, UserRole newRole) async {
    final db = await _databaseHelper.database;
    
    return await db.update(
      AppConstants.usersTable,
      {'role': newRole.index},
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  /// الحصول على إحصائيات المستخدمين
  Future<Map<String, dynamic>> getUserStatistics() async {
    final db = await _databaseHelper.database;
    
    final totalUsers = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.usersTable}
    ''');
    
    final activeUsers = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.usersTable} WHERE is_active = 1
    ''');
    
    final adminUsers = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.usersTable} 
      WHERE role = ? AND is_active = 1
    ''', [UserRole.admin.index]);
    
    final employeeUsers = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.usersTable} 
      WHERE role = ? AND is_active = 1
    ''', [UserRole.employee.index]);
    
    return {
      'total': totalUsers.first['count'],
      'active': activeUsers.first['count'],
      'admins': adminUsers.first['count'],
      'employees': employeeUsers.first['count'],
    };
  }
}
