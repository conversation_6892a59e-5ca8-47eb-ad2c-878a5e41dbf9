import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../models/models.dart';
import '../../providers/providers.dart';
import 'add_edit_product_screen.dart';

/// شاشة تفاصيل المنتج
/// Product Details Screen
class ProductDetailsScreen extends StatefulWidget {
  final Product product;

  const ProductDetailsScreen({super.key, required this.product});

  @override
  State<ProductDetailsScreen> createState() => _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends State<ProductDetailsScreen> {
  late Product _currentProduct;

  @override
  void initState() {
    super.initState();
    _currentProduct = widget.product;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_currentProduct.name),
        actions: [
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              if (authProvider.hasPermission('edit_product')) {
                return IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: _editProduct,
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProductHeader(),
            const SizedBox(height: AppConstants.largePadding),
            _buildBasicInfo(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildPricingInfo(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildInventoryInfo(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildStockActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildProductHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Row(
          children: [
            _buildProductImage(),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _currentProduct.name,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (_currentProduct.description != null &&
                      _currentProduct.description!.isNotEmpty)
                    Text(
                      _currentProduct.description!,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  const SizedBox(height: AppConstants.smallPadding),
                  _buildStockStatusBadge(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductImage() {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.primaryColor.withOpacity(0.2)),
      ),
      child:
          _currentProduct.imagePath != null &&
              _currentProduct.imagePath!.isNotEmpty
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.asset(
                _currentProduct.imagePath!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    _buildPlaceholderIcon(),
              ),
            )
          : _buildPlaceholderIcon(),
    );
  }

  Widget _buildPlaceholderIcon() {
    return const Icon(
      Icons.inventory_2,
      color: AppTheme.primaryColor,
      size: 50,
    );
  }

  Widget _buildStockStatusBadge() {
    final status = _currentProduct.stockStatus;
    final color = Color(int.parse(status.color.replaceFirst('#', '0xFF')));

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(_getStatusIcon(status), size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            status.displayName,
            style: TextStyle(color: color, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الأساسية',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildInfoRow('الاسم', _currentProduct.name, Icons.inventory_2),
            if (_currentProduct.description != null &&
                _currentProduct.description!.isNotEmpty)
              _buildInfoRow(
                'الوصف',
                _currentProduct.description!,
                Icons.description,
              ),
            if (_currentProduct.barcode != null &&
                _currentProduct.barcode!.isNotEmpty)
              _buildInfoRow(
                'الباركود',
                _currentProduct.barcode!,
                Icons.qr_code,
              ),
            Consumer<CategoryProvider>(
              builder: (context, categoryProvider, child) {
                final category = categoryProvider.getCategoryById(
                  _currentProduct.categoryId,
                );
                return _buildInfoRow(
                  'الفئة',
                  category?.name ?? 'غير محدد',
                  Icons.category,
                );
              },
            ),
            _buildInfoRow(
              'تاريخ الإنشاء',
              _formatDateTime(_currentProduct.createdAt),
              Icons.calendar_today,
            ),
            if (_currentProduct.updatedAt != null)
              _buildInfoRow(
                'آخر تحديث',
                _formatDateTime(_currentProduct.updatedAt!),
                Icons.update,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الأسعار',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Consumer<SettingsProvider>(
              builder: (context, settingsProvider, child) {
                return Column(
                  children: [
                    _buildPriceRow(
                      'سعر الشراء',
                      settingsProvider.formatCurrency(
                        _currentProduct.purchasePrice,
                      ),
                      AppTheme.warningColor,
                    ),
                    const Divider(),
                    _buildPriceRow(
                      'سعر البيع',
                      settingsProvider.formatCurrency(
                        _currentProduct.sellingPrice,
                      ),
                      AppTheme.primaryColor,
                    ),
                  ],
                );
              },
            ),
            const Divider(),
            _buildPriceRow(
              'هامش الربح',
              '${_currentProduct.profitMargin.toStringAsFixed(1)}%',
              AppTheme.successColor,
            ),
            const Divider(),
            _buildPriceRow(
              'الربح المتوقع',
              '${_currentProduct.expectedProfit.toStringAsFixed(2)} ${AppConstants.currencySymbol}',
              AppTheme.successColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInventoryInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المخزون',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: _buildInventoryCard(
                    'الكمية الحالية',
                    '${_currentProduct.quantity}',
                    Icons.inventory,
                    _getQuantityColor(),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: _buildInventoryCard(
                    'الحد الأدنى',
                    '${_currentProduct.minQuantity}',
                    Icons.warning,
                    AppTheme.warningColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStockActions() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (!authProvider.hasPermission('edit_product')) {
          return const SizedBox.shrink();
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجراءات المخزون',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () =>
                            _showQuantityDialog(TransactionType.stockIn),
                        icon: const Icon(Icons.add),
                        label: const Text('إضافة مخزون'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.successColor,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () =>
                            _showQuantityDialog(TransactionType.stockOut),
                        icon: const Icon(Icons.remove),
                        label: const Text('إخراج مخزون'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.warningColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppTheme.textSecondary),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: Theme.of(context).textTheme.bodyLarge),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  Widget _buildInventoryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: color),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getQuantityColor() {
    if (_currentProduct.isOutOfStock) return AppTheme.errorColor;
    if (_currentProduct.isLowStock) return AppTheme.warningColor;
    return AppTheme.successColor;
  }

  IconData _getStatusIcon(StockStatus status) {
    switch (status) {
      case StockStatus.inStock:
        return Icons.check_circle;
      case StockStatus.lowStock:
        return Icons.warning;
      case StockStatus.outOfStock:
        return Icons.error;
    }
  }

  void _editProduct() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditProductScreen(product: _currentProduct),
      ),
    ).then((_) {
      // تحديث المنتج بعد العودة من شاشة التعديل
      _refreshProduct();
    });
  }

  void _refreshProduct() {
    final productProvider = Provider.of<ProductProvider>(
      context,
      listen: false,
    );
    productProvider.refreshProduct(_currentProduct.id!).then((_) {
      final updatedProduct = productProvider.getProductById(
        _currentProduct.id!,
      );
      if (updatedProduct != null) {
        setState(() {
          _currentProduct = updatedProduct;
        });
      }
    });
  }

  void _showQuantityDialog(TransactionType type) {
    final quantityController = TextEditingController();
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          type == TransactionType.stockIn ? 'إضافة مخزون' : 'إخراج مخزون',
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: quantityController,
              decoration: const InputDecoration(
                labelText: 'الكمية',
                hintText: '0',
              ),
              keyboardType: TextInputType.number,
              autofocus: true,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                hintText: 'أدخل ملاحظات',
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final quantity = int.tryParse(quantityController.text);
              if (quantity == null || quantity <= 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('يرجى إدخال كمية صحيحة'),
                    backgroundColor: AppTheme.errorColor,
                  ),
                );
                return;
              }

              Navigator.pop(context);

              final authProvider = Provider.of<AuthProvider>(
                context,
                listen: false,
              );
              final productProvider = Provider.of<ProductProvider>(
                context,
                listen: false,
              );

              int newQuantity;
              if (type == TransactionType.stockIn) {
                newQuantity = _currentProduct.quantity + quantity;
              } else {
                newQuantity = _currentProduct.quantity - quantity;
                if (newQuantity < 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('الكمية المطلوبة أكبر من المتوفر'),
                      backgroundColor: AppTheme.errorColor,
                    ),
                  );
                  return;
                }
              }

              final success = await productProvider.updateProductQuantity(
                _currentProduct.id!,
                newQuantity,
                authProvider.currentUser!.id!,
                type,
                notes: notesController.text.trim().isNotEmpty
                    ? notesController.text.trim()
                    : null,
              );

              if (mounted) {
                if (success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تحديث المخزون بنجاح'),
                      backgroundColor: AppTheme.successColor,
                    ),
                  );
                  _refreshProduct();
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        productProvider.errorMessage ?? 'فشل في تحديث المخزون',
                      ),
                      backgroundColor: AppTheme.errorColor,
                    ),
                  );
                }
              }
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} - ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
