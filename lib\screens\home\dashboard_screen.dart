import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/auth_provider.dart';
import '../../providers/product_provider.dart';

/// شاشة لوحة التحكم الرئيسية
/// Dashboard Screen
class DashboardScreen extends StatefulWidget {
  final Function(int)? onNavigateToTab;

  const DashboardScreen({super.key, this.onNavigateToTab});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // تحميل البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ProductProvider>(context, listen: false).loadProducts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthProvider, ProductProvider>(
      builder: (context, authProvider, productProvider, child) {
        final user = authProvider.currentUser;
        final isAdmin = user?.role.name == 'admin';

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.blue.shade50, Colors.white],
            ),
          ),
          child: RefreshIndicator(
            onRefresh: () => productProvider.loadProducts(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ترحيب بالمستخدم
                  _buildModernWelcomeCard(user, isAdmin),
                  const SizedBox(height: 20),

                  // الإحصائيات السريعة
                  _buildModernQuickStats(productProvider),
                  const SizedBox(height: 20),

                  // تنبيهات المخزون
                  if (productProvider.lowStockCount > 0 ||
                      productProvider.outOfStockCount > 0) ...[
                    _buildStockAlerts(productProvider),
                    const SizedBox(height: 20),
                  ],

                  // الإجراءات السريعة
                  _buildModernQuickActions(isAdmin),
                  const SizedBox(height: 20),

                  // المنتجات الأخيرة
                  _buildRecentProducts(productProvider),

                  // مساحة إضافية في النهاية لمنع التداخل مع شريط التنقل
                  const SizedBox(height: 120),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildModernWelcomeCard(user, bool isAdmin) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: [Colors.blue.shade600, Colors.blue.shade800],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Icon(
              isAdmin ? Icons.admin_panel_settings : Icons.person,
              color: Colors.white,
              size: 30,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مرحباً، ${user?.fullName ?? user?.username ?? 'مستخدم'}',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  isAdmin ? 'مدير النظام' : 'موظف المخزن',
                  style: const TextStyle(fontSize: 14, color: Colors.white70),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'نشط الآن',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernQuickStats(ProductProvider productProvider) {
    if (productProvider.isLoading) {
      return const SizedBox(
        height: 120,
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإحصائيات السريعة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // الصف الأول
        Row(
          children: [
            Expanded(
              child: _buildModernStatCard(
                'إجمالي المنتجات',
                '${productProvider.totalProducts}',
                Icons.inventory_2_outlined,
                Colors.blue,
                Colors.blue.shade50,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildModernStatCard(
                'الفئات',
                '4',
                Icons.category_outlined,
                Colors.green,
                Colors.green.shade50,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // الصف الثاني
        Row(
          children: [
            Expanded(
              child: _buildModernStatCard(
                'كمية قليلة',
                '${productProvider.lowStockCount}',
                Icons.trending_down_outlined,
                Colors.orange,
                Colors.orange.shade50,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildModernStatCard(
                'نفد المخزون',
                '${productProvider.outOfStockCount}',
                Icons.warning_outlined,
                Colors.red,
                Colors.red.shade50,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildModernStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    Color backgroundColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 24, color: color),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildStockAlerts(ProductProvider productProvider) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.warning, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'تنبيهات المخزون',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),

            if (productProvider.outOfStockCount > 0)
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.error, color: Colors.red, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      '${productProvider.outOfStockCount} منتج نفد من المخزون',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ],
                ),
              ),

            if (productProvider.lowStockCount > 0)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.warning, color: Colors.orange, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      '${productProvider.lowStockCount} منتج بكمية قليلة',
                      style: const TextStyle(color: Colors.orange),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernQuickActions(bool isAdmin) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // الصف الأول
        Row(
          children: [
            Expanded(
              child: _buildModernActionCard(
                'المنتجات',
                Icons.inventory_2_outlined,
                Colors.blue,
                Colors.blue.shade50,
                () => _navigateToTab(1),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildModernActionCard(
                'الفئات',
                Icons.category_outlined,
                Colors.green,
                Colors.green.shade50,
                () => _navigateToTab(2),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // الصف الثاني
        Row(
          children: [
            if (isAdmin) ...[
              Expanded(
                child: _buildModernActionCard(
                  'التقارير',
                  Icons.analytics_outlined,
                  Colors.purple,
                  Colors.purple.shade50,
                  () => _navigateToTab(3),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildModernActionCard(
                  'الإعدادات',
                  Icons.settings_outlined,
                  Colors.grey,
                  Colors.grey.shade100,
                  () => _navigateToTab(4),
                ),
              ),
            ] else ...[
              Expanded(
                child: _buildModernActionCard(
                  'الإعدادات',
                  Icons.settings_outlined,
                  Colors.grey,
                  Colors.grey.shade100,
                  () => _navigateToTab(3),
                ),
              ),
              const Expanded(child: SizedBox()), // مساحة فارغة
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildModernActionCard(
    String title,
    IconData icon,
    Color color,
    Color backgroundColor,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, size: 28, color: color),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentProducts(ProductProvider productProvider) {
    final recentProducts = productProvider.products.take(2).toList();

    if (recentProducts.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المنتجات الأخيرة',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        // قائمة مضغوطة للمنتجات
        Column(
          children: recentProducts
              .map(
                (product) => Card(
                  margin: const EdgeInsets.only(bottom: 6),
                  child: ListTile(
                    dense: true, // تقليل الارتفاع
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    leading: CircleAvatar(
                      radius: 18,
                      backgroundColor: Colors.blue.withValues(alpha: 0.1),
                      child: const Icon(
                        Icons.inventory_2,
                        color: Colors.blue,
                        size: 18,
                      ),
                    ),
                    title: Text(
                      product.name,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(fontSize: 14),
                    ),
                    subtitle: Text(
                      'الكمية: ${product.quantity}',
                      style: const TextStyle(fontSize: 12),
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: product.isOutOfStock
                            ? Colors.red
                            : product.isLowStock
                            ? Colors.orange
                            : Colors.green,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        product.isOutOfStock
                            ? 'نفد'
                            : product.isLowStock
                            ? 'قليل'
                            : 'متوفر',
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              )
              .toList(),
        ),
      ],
    );
  }

  void _navigateToTab(int index) {
    if (widget.onNavigateToTab != null) {
      widget.onNavigateToTab!(index);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('الانتقال إلى التبويب ${index + 1}'),
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }
}
