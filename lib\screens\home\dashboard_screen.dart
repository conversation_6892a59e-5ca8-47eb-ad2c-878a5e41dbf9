import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/auth_provider.dart';
import '../../providers/product_provider.dart';

/// شاشة لوحة التحكم الرئيسية
/// Dashboard Screen
class DashboardScreen extends StatefulWidget {
  final Function(int)? onNavigateToTab;

  const DashboardScreen({super.key, this.onNavigateToTab});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // تحميل البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ProductProvider>(context, listen: false).loadProducts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthProvider, ProductProvider>(
      builder: (context, authProvider, productProvider, child) {
        final user = authProvider.currentUser;
        final isAdmin = user?.role.name == 'admin';

        return RefreshIndicator(
          onRefresh: () => productProvider.loadProducts(),
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // ترحيب بالمستخدم
                _buildWelcomeCard(user, isAdmin),
                const SizedBox(height: 20),

                // الإحصائيات السريعة
                _buildQuickStats(productProvider),
                const SizedBox(height: 20),

                // تنبيهات المخزون
                if (productProvider.lowStockCount > 0 ||
                    productProvider.outOfStockCount > 0)
                  _buildStockAlerts(productProvider),

                const SizedBox(height: 20),

                // الإجراءات السريعة
                _buildQuickActions(isAdmin),
                const SizedBox(height: 20),

                // المنتجات الأخيرة
                _buildRecentProducts(productProvider),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildWelcomeCard(user, bool isAdmin) {
    return Card(
      elevation: 4,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.blue.shade400, Colors.blue.shade600],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: Colors.white,
              child: Icon(
                isAdmin ? Icons.admin_panel_settings : Icons.person,
                color: Colors.blue,
                size: 30,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'مرحباً، ${user?.fullName ?? user?.username ?? 'مستخدم'}',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'الدور: ${isAdmin ? 'مدير النظام' : 'موظف المخزن'}',
                    style: const TextStyle(fontSize: 14, color: Colors.white70),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'آخر دخول: ${DateTime.now().toString().substring(0, 16)}',
                    style: const TextStyle(fontSize: 12, color: Colors.white60),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(ProductProvider productProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإحصائيات السريعة',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        if (productProvider.isLoading)
          const SizedBox(
            height: 120,
            child: Center(child: CircularProgressIndicator()),
          )
        else
          SizedBox(
            height: 160, // ارتفاع ثابت لمنع التداخل
            child: GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.8, // نسبة أفضل
              children: [
                _buildStatCard(
                  'إجمالي المنتجات',
                  '${productProvider.totalProducts}',
                  Icons.inventory_2,
                  Colors.blue,
                ),
                _buildStatCard('الفئات', '4', Icons.category, Colors.green),
                _buildStatCard(
                  'كمية قليلة',
                  '${productProvider.lowStockCount}',
                  Icons.trending_down,
                  Colors.orange,
                ),
                _buildStatCard(
                  'نفد المخزون',
                  '${productProvider.outOfStockCount}',
                  Icons.warning,
                  Colors.red,
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: color.withValues(alpha: 0.1),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStockAlerts(ProductProvider productProvider) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.warning, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'تنبيهات المخزون',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),

            if (productProvider.outOfStockCount > 0)
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.error, color: Colors.red, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      '${productProvider.outOfStockCount} منتج نفد من المخزون',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ],
                ),
              ),

            if (productProvider.lowStockCount > 0)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.warning, color: Colors.orange, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      '${productProvider.lowStockCount} منتج بكمية قليلة',
                      style: const TextStyle(color: Colors.orange),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(bool isAdmin) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        SizedBox(
          height: 140, // ارتفاع ثابت
          child: GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.5, // نسبة محسنة
            children: [
              _buildActionCard(
                'عرض المنتجات',
                Icons.inventory,
                Colors.blue,
                () => _navigateToTab(1),
              ),
              _buildActionCard(
                'إدارة الفئات',
                Icons.category,
                Colors.green,
                () => _navigateToTab(2),
              ),
              if (isAdmin) ...[
                _buildActionCard(
                  'التقارير',
                  Icons.analytics,
                  Colors.purple,
                  () => _navigateToTab(3),
                ),
                _buildActionCard(
                  'الإعدادات',
                  Icons.settings,
                  Colors.grey,
                  () => _navigateToTab(isAdmin ? 4 : 3),
                ),
              ] else
                _buildActionCard(
                  'الإعدادات',
                  Icons.settings,
                  Colors.grey,
                  () => _navigateToTab(3),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 40, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentProducts(ProductProvider productProvider) {
    final recentProducts = productProvider.products.take(3).toList();

    if (recentProducts.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المنتجات الأخيرة',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        // استخدام Container مع ارتفاع محدد
        SizedBox(
          height: 200, // ارتفاع ثابت لمنع التداخل
          child: ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            itemCount: recentProducts.length,
            itemBuilder: (context, index) {
              final product = recentProducts[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.blue.withValues(alpha: 0.1),
                    child: const Icon(Icons.inventory_2, color: Colors.blue),
                  ),
                  title: Text(
                    product.name,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  subtitle: Text('الكمية: ${product.quantity}'),
                  trailing: Chip(
                    label: Text(
                      product.isOutOfStock
                          ? 'نفد'
                          : product.isLowStock
                          ? 'قليل'
                          : 'متوفر',
                      style: const TextStyle(fontSize: 10, color: Colors.white),
                    ),
                    backgroundColor: product.isOutOfStock
                        ? Colors.red
                        : product.isLowStock
                        ? Colors.orange
                        : Colors.green,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _navigateToTab(int index) {
    if (widget.onNavigateToTab != null) {
      widget.onNavigateToTab!(index);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('الانتقال إلى التبويب ${index + 1}'),
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }
}
