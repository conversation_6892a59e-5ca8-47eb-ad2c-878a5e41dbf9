{"buildFiles": ["C:\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\augment-projects\\stock\\stock_management_app\\build\\.cxx\\Debug\\3s50jf6p\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\augment-projects\\stock\\stock_management_app\\build\\.cxx\\Debug\\3s50jf6p\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}