name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Get version from tag
      id: get_version
      run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
      
    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Stock Management App ${{ steps.get_version.outputs.VERSION }}
        body: |
          ## تطبيق إدارة المخازن - الإصدار ${{ steps.get_version.outputs.VERSION }}
          
          ### ✨ المميزات الجديدة
          - تحسينات في الأداء
          - إصلاحات الأخطاء
          - تحديثات الأمان
          
          ### 📱 ملفات التحميل
          - **Android APK**: للهواتف والأجهزة اللوحية التي تعمل بنظام Android
          - **iOS IPA**: للأجهزة التي تعمل بنظام iOS (يتطلب تثبيت عبر Xcode أو TestFlight)
          
          ### 📋 متطلبات النظام
          - **Android**: 5.0 (API level 21) أو أحدث
          - **iOS**: 11.0 أو أحدث
          
          ### 🔐 بيانات تسجيل الدخول الافتراضية
          - **مدير**: admin / admin123
          - **موظف**: employee / emp123
          
          ### 📞 الدعم
          للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى فتح issue في المستودع.
        draft: false
        prerelease: false

  build-android:
    name: Build Android Release
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
        
    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '17'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Build APK
      run: flutter build apk --release
      
    - name: Build App Bundle
      run: flutter build appbundle --release
      
    - name: Sign APK
      run: |
        echo "APK signing would happen here with proper keystore"
        
    - name: Upload APK to Release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: build/app/outputs/flutter-apk/app-release.apk
        asset_name: stock-management-app-android.apk
        asset_content_type: application/vnd.android.package-archive
        
    - name: Upload App Bundle to Release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: build/app/outputs/bundle/release/app-release.aab
        asset_name: stock-management-app-android.aab
        asset_content_type: application/octet-stream

  build-ios:
    name: Build iOS Release
    runs-on: macos-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Build iOS
      run: flutter build ios --release --no-codesign
      
    - name: Create IPA
      run: |
        cd build/ios/iphoneos
        mkdir -p Payload
        cp -r Runner.app Payload/
        zip -r stock-management-app-ios.ipa Payload/
        
    - name: Upload IPA to Release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: build/ios/iphoneos/stock-management-app-ios.ipa
        asset_name: stock-management-app-ios.ipa
        asset_content_type: application/octet-stream

  build-web:
    name: Build Web Release
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Build Web
      run: flutter build web --release
      
    - name: Create Web Archive
      run: |
        cd build/web
        zip -r ../../stock-management-app-web.zip .
        
    - name: Upload Web to Release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: stock-management-app-web.zip
        asset_name: stock-management-app-web.zip
        asset_content_type: application/zip

  deploy-web:
    name: Deploy Web to GitHub Pages
    runs-on: ubuntu-latest
    needs: build-web
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Build Web
      run: flutter build web --release --base-href "/stock-management-app/"
      
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: build/web

  notify-release:
    name: Notify Release
    runs-on: ubuntu-latest
    needs: [build-android, build-ios, build-web]
    if: always()
    
    steps:
    - name: Get version from tag
      id: get_version
      run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
      
    - name: Notify on success
      if: needs.build-android.result == 'success' && needs.build-ios.result == 'success'
      run: |
        echo "🎉 Release ${{ steps.get_version.outputs.VERSION }} created successfully!"
        echo "✅ Android APK built and uploaded"
        echo "✅ iOS IPA built and uploaded"
        echo "✅ Web version built and deployed"
        
    - name: Notify on failure
      if: needs.build-android.result == 'failure' || needs.build-ios.result == 'failure'
      run: |
        echo "❌ Release ${{ steps.get_version.outputs.VERSION }} failed!"
        echo "Please check the build logs for more information"
