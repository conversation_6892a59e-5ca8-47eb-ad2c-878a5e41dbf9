import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../models/models.dart';
import '../../providers/providers.dart';
import '../../widgets/search_bar_widget.dart';

/// شاشة إدارة الصلاحيات
/// Permissions Management Screen
class PermissionsManagementScreen extends StatefulWidget {
  final User user;

  const PermissionsManagementScreen({
    super.key,
    required this.user,
  });

  @override
  State<PermissionsManagementScreen> createState() => _PermissionsManagementScreenState();
}

class _PermissionsManagementScreenState extends State<PermissionsManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadData() {
    final permissionProvider = Provider.of<PermissionProvider>(context, listen: false);
    permissionProvider.loadPermissions();
    permissionProvider.loadUserPermissions(widget.user.id!);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('صلاحيات ${widget.user.fullName.isNotEmpty ? widget.user.fullName : widget.user.username}'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showCopyPermissionsDialog,
            icon: const Icon(Icons.copy),
            tooltip: 'نسخ صلاحيات',
          ),
          IconButton(
            onPressed: _savePermissions,
            icon: const Icon(Icons.save),
            tooltip: 'حفظ',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryColor.withValues(alpha: 0.1),
              Colors.white,
            ],
          ),
        ),
        child: Column(
          children: [
            _buildSearchSection(),
            Expanded(child: _buildPermissionsList()),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          SearchBarWidget(
            controller: _searchController,
            hintText: 'البحث في الصلاحيات...',
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
            onClear: () {
              setState(() {
                _searchQuery = '';
              });
            },
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Consumer<PermissionProvider>(
      builder: (context, permissionProvider, child) {
        return Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _selectAllPermissions(true),
                icon: const Icon(Icons.select_all),
                label: const Text('تحديد الكل'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.successColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _selectAllPermissions(false),
                icon: const Icon(Icons.deselect),
                label: const Text('إلغاء الكل'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.errorColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPermissionsList() {
    return Consumer<PermissionProvider>(
      builder: (context, permissionProvider, child) {
        if (permissionProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (permissionProvider.errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  permissionProvider.errorMessage!,
                  style: TextStyle(color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadData,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        final categorizedPermissions = permissionProvider.categorizedPermissions;
        if (categorizedPermissions.isEmpty) {
          return const Center(
            child: Text('لا توجد صلاحيات'),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          itemCount: categorizedPermissions.keys.length,
          itemBuilder: (context, index) {
            final category = categorizedPermissions.keys.elementAt(index);
            final permissions = categorizedPermissions[category]!;
            
            // تصفية الصلاحيات حسب البحث
            final filteredPermissions = _searchQuery.isEmpty
                ? permissions
                : permissions.where((permission) =>
                    permission.displayName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                    permission.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                    permission.name.toLowerCase().contains(_searchQuery.toLowerCase())
                  ).toList();

            if (filteredPermissions.isEmpty) {
              return const SizedBox.shrink();
            }

            return _buildCategoryCard(category, filteredPermissions, permissionProvider);
          },
        );
      },
    );
  }

  Widget _buildCategoryCard(
    String category,
    List<Permission> permissions,
    PermissionProvider permissionProvider,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: ExpansionTile(
        title: Text(
          category,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text('${permissions.length} صلاحية'),
        children: permissions.map((permission) {
          return _buildPermissionTile(permission, permissionProvider);
        }).toList(),
      ),
    );
  }

  Widget _buildPermissionTile(
    Permission permission,
    PermissionProvider permissionProvider,
  ) {
    final hasPermission = permissionProvider.hasUserPermission(permission.id!);

    return ListTile(
      title: Text(permission.displayName),
      subtitle: Text(permission.description),
      trailing: Switch(
        value: hasPermission,
        onChanged: (value) {
          permissionProvider.toggleUserPermission(widget.user.id!, permission.id!);
        },
        activeColor: AppTheme.successColor,
      ),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: hasPermission 
              ? AppTheme.successColor.withValues(alpha: 0.1)
              : Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          hasPermission ? Icons.check_circle : Icons.radio_button_unchecked,
          color: hasPermission ? AppTheme.successColor : Colors.grey,
          size: 20,
        ),
      ),
    );
  }

  void _selectAllPermissions(bool select) {
    final permissionProvider = Provider.of<PermissionProvider>(context, listen: false);
    
    for (final category in permissionProvider.categorizedPermissions.values) {
      for (final permission in category) {
        if (select) {
          permissionProvider.grantPermission(widget.user.id!, permission.id!);
        } else {
          permissionProvider.revokePermission(widget.user.id!, permission.id!);
        }
      }
    }
  }

  void _savePermissions() async {
    final permissionProvider = Provider.of<PermissionProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    final selectedPermissions = permissionProvider.getSelectedPermissionIds();
    
    final success = await permissionProvider.updateUserPermissions(
      widget.user.id!,
      selectedPermissions,
    );

    if (success) {
      // إعادة تحميل صلاحيات المستخدم الحالي إذا كان هو نفسه
      if (authProvider.currentUser?.id == widget.user.id) {
        await authProvider.reloadUserPermissions();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الصلاحيات بنجاح'),
            backgroundColor: AppTheme.successColor,
          ),
        );
        Navigator.of(context).pop();
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(permissionProvider.errorMessage ?? 'فشل في حفظ الصلاحيات'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _showCopyPermissionsDialog() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final otherUsers = authProvider.allUsers.where((user) => user.id != widget.user.id).toList();

    if (otherUsers.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لا يوجد مستخدمين آخرين لنسخ الصلاحيات منهم')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نسخ صلاحيات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر المستخدم لنسخ صلاحياته:'),
            const SizedBox(height: 16),
            ...otherUsers.map((user) => ListTile(
              title: Text(user.fullName.isNotEmpty ? user.fullName : user.username),
              subtitle: Text(user.role.displayName),
              onTap: () {
                Navigator.of(context).pop();
                _copyPermissionsFrom(user);
              },
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _copyPermissionsFrom(User fromUser) async {
    final permissionProvider = Provider.of<PermissionProvider>(context, listen: false);
    
    final success = await permissionProvider.copyPermissions(
      fromUser.id!,
      widget.user.id!,
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم نسخ الصلاحيات من ${fromUser.fullName.isNotEmpty ? fromUser.fullName : fromUser.username}'),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
  }
}
