import '../database/database_helper.dart';
import '../models/models.dart';
import '../constants/app_constants.dart';

/// خدمة المشتريات
/// Purchase Service for managing purchase operations
class PurchaseService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء مشترى جديد
  Future<int> createPurchase(Purchase purchase, List<PurchaseItem> items) async {
    final db = await _databaseHelper.database;

    return await db.transaction((txn) async {
      // إدراج المشترى
      final purchaseId = await txn.insert(
        AppConstants.purchasesTable,
        purchase.toMap(),
      );

      // إدراج عناصر المشترى
      for (final item in items) {
        final itemWithPurchaseId = item.copyWith(purchaseId: purchaseId);
        await txn.insert(
          AppConstants.purchaseItemsTable,
          itemWithPurchaseId.toMap(),
        );

        // تحديث كمية المنتج في المخزون
        await txn.rawUpdate('''
          UPDATE ${AppConstants.productsTable} 
          SET quantity = quantity + ?, updated_at = ?
          WHERE id = ?
        ''', [item.quantity, DateTime.now().toIso8601String(), item.productId]);

        // إضافة سجل العملية
        final transactionLog = TransactionLog(
          productId: item.productId,
          userId: purchase.userId,
          type: TransactionType.stockIn,
          quantity: item.quantity,
          previousQuantity: 0, // سيتم تحديثه لاحقاً
          newQuantity: 0, // سيتم تحديثه لاحقاً
          notes: 'مشترى رقم: ${purchase.invoiceNumber}',
          createdAt: DateTime.now(),
        );

        await txn.insert(
          AppConstants.transactionLogsTable,
          transactionLog.toMap(),
        );
      }

      return purchaseId;
    });
  }

  /// الحصول على جميع المشتريات
  Future<List<Purchase>> getAllPurchases() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.purchasesTable,
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Purchase.fromMap(maps[i]);
    });
  }

  /// الحصول على مشترى بالمعرف
  Future<Purchase?> getPurchaseById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.purchasesTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Purchase.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على عناصر المشترى
  Future<List<PurchaseItem>> getPurchaseItems(int purchaseId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.purchaseItemsTable,
      where: 'purchase_id = ?',
      whereArgs: [purchaseId],
      orderBy: 'created_at ASC',
    );

    return List.generate(maps.length, (i) {
      return PurchaseItem.fromMap(maps[i]);
    });
  }

  /// تحديث حالة المشترى
  Future<int> updatePurchaseStatus(int purchaseId, PurchaseStatus status) async {
    final db = await _databaseHelper.database;

    return await db.update(
      AppConstants.purchasesTable,
      {
        'status': status.index,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [purchaseId],
    );
  }

  /// تحديث دفعة المشترى
  Future<int> updatePurchasePayment(int purchaseId, double paidAmount) async {
    final db = await _databaseHelper.database;

    // الحصول على المشترى الحالي
    final purchase = await getPurchaseById(purchaseId);
    if (purchase == null) return 0;

    final newPaidAmount = purchase.paidAmount + paidAmount;
    final newRemainingAmount = purchase.totalAmount - newPaidAmount;

    return await db.update(
      AppConstants.purchasesTable,
      {
        'paid_amount': newPaidAmount,
        'remaining_amount': newRemainingAmount,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [purchaseId],
    );
  }

  /// حذف مشترى
  Future<int> deletePurchase(int purchaseId) async {
    final db = await _databaseHelper.database;

    return await db.transaction((txn) async {
      // حذف عناصر المشترى
      await txn.delete(
        AppConstants.purchaseItemsTable,
        where: 'purchase_id = ?',
        whereArgs: [purchaseId],
      );

      // حذف المشترى
      return await txn.delete(
        AppConstants.purchasesTable,
        where: 'id = ?',
        whereArgs: [purchaseId],
      );
    });
  }

  /// البحث في المشتريات
  Future<List<Purchase>> searchPurchases(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.purchasesTable,
      where: '''
        invoice_number LIKE ? OR 
        supplier_name LIKE ? OR 
        notes LIKE ?
      ''',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Purchase.fromMap(maps[i]);
    });
  }

  /// الحصول على المشتريات حسب التاريخ
  Future<List<Purchase>> getPurchasesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.purchasesTable,
      where: 'purchase_date BETWEEN ? AND ?',
      whereArgs: [
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
      orderBy: 'purchase_date DESC',
    );

    return List.generate(maps.length, (i) {
      return Purchase.fromMap(maps[i]);
    });
  }

  /// الحصول على المشتريات حسب الحالة
  Future<List<Purchase>> getPurchasesByStatus(PurchaseStatus status) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.purchasesTable,
      where: 'status = ?',
      whereArgs: [status.index],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Purchase.fromMap(maps[i]);
    });
  }

  /// الحصول على إحصائيات المشتريات
  Future<Map<String, dynamic>> getPurchaseStats() async {
    final db = await _databaseHelper.database;

    // إجمالي المشتريات
    final totalPurchases = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.purchasesTable}
    ''');

    // إجمالي قيمة المشتريات
    final totalValue = await db.rawQuery('''
      SELECT SUM(total_amount) as total FROM ${AppConstants.purchasesTable}
    ''');

    // إجمالي المدفوع
    final totalPaid = await db.rawQuery('''
      SELECT SUM(paid_amount) as total FROM ${AppConstants.purchasesTable}
    ''');

    // إجمالي المتبقي
    final totalRemaining = await db.rawQuery('''
      SELECT SUM(remaining_amount) as total FROM ${AppConstants.purchasesTable}
    ''');

    // المشتريات المكتملة
    final completedPurchases = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.purchasesTable} 
      WHERE status = ${PurchaseStatus.completed.index}
    ''');

    // المشتريات المعلقة
    final pendingPurchases = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.purchasesTable} 
      WHERE status = ${PurchaseStatus.pending.index}
    ''');

    return {
      'total_purchases': totalPurchases.first['count'],
      'total_value': totalValue.first['total'] ?? 0.0,
      'total_paid': totalPaid.first['total'] ?? 0.0,
      'total_remaining': totalRemaining.first['total'] ?? 0.0,
      'completed_purchases': completedPurchases.first['count'],
      'pending_purchases': pendingPurchases.first['count'],
    };
  }

  /// توليد رقم فاتورة جديد
  Future<String> generateInvoiceNumber() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.purchasesTable}
    ''');

    final count = result.first['count'] as int;
    final now = DateTime.now();
    return 'PUR-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${(count + 1).toString().padLeft(4, '0')}';
  }
}
