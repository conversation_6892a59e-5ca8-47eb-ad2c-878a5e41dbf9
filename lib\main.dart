import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'constants/app_constants.dart';
import 'providers/providers.dart';
import 'screens/products/add_product_screen.dart';
import 'screens/purchases/add_purchase_screen.dart';
import 'screens/sales/add_sale_screen.dart';
import 'screens/splash/splash_screen.dart';
import 'utils/database_fixer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // إصلاح قاعدة البيانات إذا كانت تحتوي على أخطاء
  await DatabaseFixer.fixDatabaseIfNeeded();

  runApp(const StockManagementApp());
}

class StockManagementApp extends StatelessWidget {
  const StockManagementApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => ProductProvider()),
        ChangeNotifierProvider(create: (_) => CategoryProvider()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
        ChangeNotifierProvider(create: (_) => PurchaseProvider()),
        ChangeNotifierProvider(create: (_) => SaleProvider()),
        ChangeNotifierProvider(create: (_) => PermissionProvider()),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        debugShowCheckedModeBanner: false,
        theme: ThemeData(primarySwatch: Colors.blue, fontFamily: 'Arial'),
        home: const SplashScreen(),
        routes: {
          AddPurchaseScreen.routeName: (context) => const AddPurchaseScreen(),
          AddSaleScreen.routeName: (context) => const AddSaleScreen(),
          AddProductScreen.routeName: (context) => const AddProductScreen(),
        },
      ),
    );
  }
}
