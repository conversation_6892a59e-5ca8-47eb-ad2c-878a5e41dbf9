import 'package:flutter/foundation.dart';

import '../models/models.dart';
import '../services/services.dart';

/// مزود المنتجات
/// Product Provider for managing product state
class ProductProvider with ChangeNotifier {
  final ProductService _productService = ProductService();
  final TransactionLogService _transactionLogService = TransactionLogService();
  
  List<Product> _products = [];
  List<Product> _filteredProducts = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _searchQuery = '';
  int? _selectedCategoryId;
  StockStatus? _selectedStockStatus;

  // Getters
  List<Product> get products => _filteredProducts;
  List<Product> get allProducts => _products;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get searchQuery => _searchQuery;
  int? get selectedCategoryId => _selectedCategoryId;
  StockStatus? get selectedStockStatus => _selectedStockStatus;
  
  int get totalProducts => _products.length;
  int get lowStockCount => _products.where((p) => p.isLowStock).length;
  int get outOfStockCount => _products.where((p) => p.isOutOfStock).length;

  /// تحميل جميع المنتجات
  Future<void> loadProducts() async {
    _setLoading(true);
    _clearError();

    try {
      _products = await _productService.getActiveProducts();
      _applyFilters();
      _setLoading(false);
    } catch (e) {
      _setError('حدث خطأ أثناء تحميل المنتجات: ${e.toString()}');
      _setLoading(false);
    }
  }

  /// إضافة منتج جديد
  Future<bool> addProduct(Product product) async {
    _setLoading(true);
    _clearError();

    try {
      final productId = await _productService.createProduct(product);
      final newProduct = product.copyWith(id: productId);
      _products.insert(0, newProduct);
      _applyFilters();
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء إضافة المنتج: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// تحديث منتج
  Future<bool> updateProduct(Product product) async {
    _setLoading(true);
    _clearError();

    try {
      await _productService.updateProduct(product);
      final index = _products.indexWhere((p) => p.id == product.id);
      if (index != -1) {
        _products[index] = product;
        _applyFilters();
      }
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء تحديث المنتج: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// حذف منتج
  Future<bool> deleteProduct(int productId) async {
    _setLoading(true);
    _clearError();

    try {
      await _productService.deleteProduct(productId);
      _products.removeWhere((p) => p.id == productId);
      _applyFilters();
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء حذف المنتج: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// تحديث كمية المنتج
  Future<bool> updateProductQuantity(
    int productId,
    int newQuantity,
    int userId,
    TransactionType type, {
    String? notes,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      await _productService.updateProductQuantity(
        productId,
        newQuantity,
        userId,
        type,
        notes: notes,
      );
      
      // تحديث المنتج في القائمة
      final index = _products.indexWhere((p) => p.id == productId);
      if (index != -1) {
        _products[index] = _products[index].copyWith(
          quantity: newQuantity,
          updatedAt: DateTime.now(),
        );
        _applyFilters();
      }
      
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء تحديث الكمية: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// البحث في المنتجات
  void searchProducts(String query) {
    _searchQuery = query;
    _applyFilters();
  }

  /// تصفية المنتجات حسب الفئة
  void filterByCategory(int? categoryId) {
    _selectedCategoryId = categoryId;
    _applyFilters();
  }

  /// تصفية المنتجات حسب حالة المخزون
  void filterByStockStatus(StockStatus? status) {
    _selectedStockStatus = status;
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery = '';
    _selectedCategoryId = null;
    _selectedStockStatus = null;
    _applyFilters();
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    _filteredProducts = _products.where((product) {
      // تصفية البحث
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final matchesName = product.name.toLowerCase().contains(query);
        final matchesDescription = product.description?.toLowerCase().contains(query) ?? false;
        final matchesBarcode = product.barcode?.toLowerCase().contains(query) ?? false;
        
        if (!matchesName && !matchesDescription && !matchesBarcode) {
          return false;
        }
      }

      // تصفية الفئة
      if (_selectedCategoryId != null && product.categoryId != _selectedCategoryId) {
        return false;
      }

      // تصفية حالة المخزون
      if (_selectedStockStatus != null && product.stockStatus != _selectedStockStatus) {
        return false;
      }

      return true;
    }).toList();

    notifyListeners();
  }

  /// الحصول على منتج بواسطة المعرف
  Product? getProductById(int id) {
    try {
      return _products.firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على منتج بواسطة الباركود
  Future<Product?> getProductByBarcode(String barcode) async {
    try {
      return await _productService.getProductByBarcode(barcode);
    } catch (e) {
      _setError('حدث خطأ أثناء البحث بالباركود: ${e.toString()}');
      return null;
    }
  }

  /// الحصول على المنتجات منخفضة المخزون
  List<Product> getLowStockProducts() {
    return _products.where((product) => product.isLowStock).toList();
  }

  /// الحصول على المنتجات نافدة المخزون
  List<Product> getOutOfStockProducts() {
    return _products.where((product) => product.isOutOfStock).toList();
  }

  /// الحصول على المنتجات حسب الفئة
  List<Product> getProductsByCategory(int categoryId) {
    return _products.where((product) => product.categoryId == categoryId).toList();
  }

  /// الحصول على إحصائيات المنتجات
  Future<Map<String, dynamic>> getProductStatistics() async {
    try {
      return await _productService.getProductStatistics();
    } catch (e) {
      _setError('حدث خطأ أثناء تحميل الإحصائيات: ${e.toString()}');
      return {};
    }
  }

  /// التحقق من صحة بيانات المنتج
  Map<String, String> validateProduct(Product product) {
    return _productService.validateProduct(product);
  }

  /// تبديل حالة المنتج (نشط/غير نشط)
  Future<bool> toggleProductStatus(int productId, bool isActive) async {
    _setLoading(true);
    _clearError();

    try {
      await _productService.toggleProductStatus(productId, isActive);
      
      final index = _products.indexWhere((p) => p.id == productId);
      if (index != -1) {
        _products[index] = _products[index].copyWith(
          isActive: isActive,
          updatedAt: DateTime.now(),
        );
        _applyFilters();
      }
      
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء تغيير حالة المنتج: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// إعادة تحميل منتج واحد
  Future<void> refreshProduct(int productId) async {
    try {
      final product = await _productService.getProductById(productId);
      if (product != null) {
        final index = _products.indexWhere((p) => p.id == productId);
        if (index != -1) {
          _products[index] = product;
          _applyFilters();
        }
      }
    } catch (e) {
      _setError('حدث خطأ أثناء تحديث المنتج: ${e.toString()}');
    }
  }

  /// ترتيب المنتجات
  void sortProducts(String sortBy, {bool ascending = true}) {
    _products.sort((a, b) {
      int comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'quantity':
          comparison = a.quantity.compareTo(b.quantity);
          break;
        case 'price':
          comparison = a.sellingPrice.compareTo(b.sellingPrice);
          break;
        case 'created_at':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        default:
          comparison = a.name.compareTo(b.name);
      }
      
      return ascending ? comparison : -comparison;
    });
    
    _applyFilters();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// إعادة تعيين حالة المزود
  void reset() {
    _products = [];
    _filteredProducts = [];
    _isLoading = false;
    _errorMessage = null;
    _searchQuery = '';
    _selectedCategoryId = null;
    _selectedStockStatus = null;
    notifyListeners();
  }
}
