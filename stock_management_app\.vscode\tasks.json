{"version": "2.0.0", "tasks": [{"label": "Flutter: Get Packages", "type": "shell", "command": "flutter", "args": ["pub", "get"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Clean", "type": "shell", "command": "flutter", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Ana<PERSON>ze", "type": "shell", "command": "flutter", "args": ["analyze"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Test", "type": "shell", "command": "flutter", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Test with Coverage", "type": "shell", "command": "flutter", "args": ["test", "--coverage"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Build APK", "type": "shell", "command": "flutter", "args": ["build", "apk", "--release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Build APK (Debug)", "type": "shell", "command": "flutter", "args": ["build", "apk", "--debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Build iOS", "type": "shell", "command": "flutter", "args": ["build", "ios", "--release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Build Web", "type": "shell", "command": "flutter", "args": ["build", "web", "--release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Format Code", "type": "shell", "command": "dart", "args": ["format", "lib/", "test/"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Doctor", "type": "shell", "command": "flutter", "args": ["doctor", "-v"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Upgrade", "type": "shell", "command": "flutter", "args": ["upgrade"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Pub Upgrade", "type": "shell", "command": "flutter", "args": ["pub", "upgrade"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Pub Outdated", "type": "shell", "command": "flutter", "args": ["pub", "outdated"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Generate Localization", "type": "shell", "command": "flutter", "args": ["gen-l10n"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}