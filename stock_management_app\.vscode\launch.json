{"version": "0.2.0", "configurations": [{"name": "Debug (Development)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--flavor", "development", "--dart-define=ENVIRONMENT=development"], "console": "debugConsole", "flutterMode": "debug"}, {"name": "Release (Production)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--flavor", "production", "--dart-define=ENVIRONMENT=production"], "console": "debugConsole", "flutterMode": "release"}, {"name": "Profile", "request": "launch", "type": "dart", "program": "lib/main.dart", "console": "debugConsole", "flutterMode": "profile"}, {"name": "Debug (Android)", "request": "launch", "type": "dart", "program": "lib/main.dart", "console": "debugConsole", "flutterMode": "debug", "deviceId": "android"}, {"name": "Debug (iOS)", "request": "launch", "type": "dart", "program": "lib/main.dart", "console": "debugConsole", "flutterMode": "debug", "deviceId": "ios"}, {"name": "Debug (Chrome)", "request": "launch", "type": "dart", "program": "lib/main.dart", "console": "debugConsole", "flutterMode": "debug", "deviceId": "chrome"}, {"name": "Debug Tests", "request": "launch", "type": "dart", "program": "test/", "console": "debugConsole"}, {"name": "Debug Widget Tests", "request": "launch", "type": "dart", "program": "test/widget_test.dart", "console": "debugConsole"}, {"name": "Debug Integration Tests", "request": "launch", "type": "dart", "program": "integration_test/", "console": "debugConsole"}], "compounds": [{"name": "Debug All Platforms", "configurations": ["Debug (Android)", "Debug (iOS)"]}]}