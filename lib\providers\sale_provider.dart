import 'package:flutter/foundation.dart';

import '../models/models.dart';
import '../services/services.dart';

/// مزود المبيعات
/// Sale Provider for managing sales state
class SaleProvider with ChangeNotifier {
  final SaleService _saleService = SaleService();

  List<Sale> _sales = [];
  List<Sale> _filteredSales = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _searchQuery = '';
  SaleStatus? _selectedStatus;
  PaymentMethod? _selectedPaymentMethod;
  DateTime? _startDate;
  DateTime? _endDate;

  // Getters
  List<Sale> get sales => _filteredSales;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get searchQuery => _searchQuery;
  SaleStatus? get selectedStatus => _selectedStatus;
  PaymentMethod? get selectedPaymentMethod => _selectedPaymentMethod;
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;

  /// تحميل جميع المبيعات
  Future<void> loadSales() async {
    _setLoading(true);
    _clearError();

    try {
      _sales = await _saleService.getAllSales();
      _applyFilters();
    } catch (e) {
      _setError('فشل في تحميل المبيعات: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// إنشاء مبيعة جديدة
  Future<bool> createSale(Sale sale, List<SaleItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleService.createSale(sale, items);
      await loadSales(); // إعادة تحميل القائمة
      return true;
    } catch (e) {
      _setError('فشل في إنشاء المبيعة: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على مبيعة بالمعرف
  Future<Sale?> getSaleById(int id) async {
    try {
      return await _saleService.getSaleById(id);
    } catch (e) {
      _setError('فشل في الحصول على المبيعة: ${e.toString()}');
      return null;
    }
  }

  /// الحصول على عناصر المبيعة
  Future<List<SaleItem>> getSaleItems(int saleId) async {
    try {
      return await _saleService.getSaleItems(saleId);
    } catch (e) {
      _setError('فشل في الحصول على عناصر المبيعة: ${e.toString()}');
      return [];
    }
  }

  /// تحديث حالة المبيعة
  Future<bool> updateSaleStatus(int saleId, SaleStatus status) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleService.updateSaleStatus(saleId, status);
      await loadSales(); // إعادة تحميل القائمة
      return true;
    } catch (e) {
      _setError('فشل في تحديث حالة المبيعة: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث دفعة المبيعة
  Future<bool> updateSalePayment(int saleId, double paidAmount) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleService.updateSalePayment(saleId, paidAmount);
      await loadSales(); // إعادة تحميل القائمة
      return true;
    } catch (e) {
      _setError('فشل في تحديث دفعة المبيعة: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// إرجاع مبيعة
  Future<bool> returnSale(int saleId, int userId) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleService.returnSale(saleId, userId);
      await loadSales(); // إعادة تحميل القائمة
      return true;
    } catch (e) {
      _setError('فشل في إرجاع المبيعة: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف مبيعة
  Future<bool> deleteSale(int saleId) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleService.deleteSale(saleId);
      await loadSales(); // إعادة تحميل القائمة
      return true;
    } catch (e) {
      _setError('فشل في حذف المبيعة: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// البحث في المبيعات
  void searchSales(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }

  /// تصفية المبيعات حسب الحالة
  void filterByStatus(SaleStatus? status) {
    _selectedStatus = status;
    _applyFilters();
    notifyListeners();
  }

  /// تصفية المبيعات حسب طريقة الدفع
  void filterByPaymentMethod(PaymentMethod? paymentMethod) {
    _selectedPaymentMethod = paymentMethod;
    _applyFilters();
    notifyListeners();
  }

  /// تصفية المبيعات حسب التاريخ
  void filterByDateRange(DateTime? startDate, DateTime? endDate) {
    _startDate = startDate;
    _endDate = endDate;
    _applyFilters();
    notifyListeners();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery = '';
    _selectedStatus = null;
    _selectedPaymentMethod = null;
    _startDate = null;
    _endDate = null;
    _applyFilters();
    notifyListeners();
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    _filteredSales = _sales.where((sale) {
      // تصفية البحث
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!sale.invoiceNumber.toLowerCase().contains(query) &&
            !(sale.customerName?.toLowerCase().contains(query) ?? false) &&
            !(sale.notes?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // تصفية الحالة
      if (_selectedStatus != null && sale.status != _selectedStatus) {
        return false;
      }

      // تصفية طريقة الدفع
      if (_selectedPaymentMethod != null && sale.paymentMethod != _selectedPaymentMethod) {
        return false;
      }

      // تصفية التاريخ
      if (_startDate != null && sale.saleDate.isBefore(_startDate!)) {
        return false;
      }
      if (_endDate != null && sale.saleDate.isAfter(_endDate!)) {
        return false;
      }

      return true;
    }).toList();
  }

  /// الحصول على إحصائيات المبيعات
  Future<Map<String, dynamic>> getSalesStats() async {
    try {
      return await _saleService.getSalesStats();
    } catch (e) {
      _setError('فشل في الحصول على إحصائيات المبيعات: ${e.toString()}');
      return {};
    }
  }

  /// توليد رقم فاتورة جديد
  Future<String> generateInvoiceNumber() async {
    try {
      return await _saleService.generateInvoiceNumber();
    } catch (e) {
      _setError('فشل في توليد رقم الفاتورة: ${e.toString()}');
      return '';
    }
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void _clearError() {
    _errorMessage = null;
  }

  /// مسح البيانات
  void clearData() {
    _sales.clear();
    _filteredSales.clear();
    _searchQuery = '';
    _selectedStatus = null;
    _selectedPaymentMethod = null;
    _startDate = null;
    _endDate = null;
    _errorMessage = null;
    notifyListeners();
  }
}
