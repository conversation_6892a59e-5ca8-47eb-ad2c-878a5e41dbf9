import '../database/database_helper.dart';
import '../models/models.dart';
import '../constants/app_constants.dart';

/// خدمة المنتجات
/// Product Service for product management operations
class ProductService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء منتج جديد
  Future<int> createProduct(Product product) async {
    final db = await _databaseHelper.database;

    // التحقق من عدم وجود باركود مكرر (إذا كان موجوداً)
    if (product.barcode != null && product.barcode!.isNotEmpty) {
      final existingProduct = await getProductByBarcode(product.barcode!);
      if (existingProduct != null) {
        throw Exception('الباركود موجود مسبقاً');
      }
    }

    return await db.insert(AppConstants.productsTable, product.toMap());
  }

  /// الحصول على منتج بواسطة المعرف
  Future<Product?> getProductById(int id) async {
    final db = await _databaseHelper.database;

    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.productsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Product.fromMap(maps.first);
    }

    return null;
  }

  /// الحصول على منتج بواسطة الباركود
  Future<Product?> getProductByBarcode(String barcode) async {
    final db = await _databaseHelper.database;

    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.productsTable,
      where: 'barcode = ?',
      whereArgs: [barcode],
    );

    if (maps.isNotEmpty) {
      return Product.fromMap(maps.first);
    }

    return null;
  }

  /// الحصول على جميع المنتجات
  Future<List<Product>> getAllProducts() async {
    final db = await _databaseHelper.database;

    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.productsTable,
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// الحصول على المنتجات النشطة
  Future<List<Product>> getActiveProducts() async {
    final db = await _databaseHelper.database;

    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.productsTable,
      where: 'is_active = 1',
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// الحصول على المنتجات حسب الفئة
  Future<List<Product>> getProductsByCategory(int categoryId) async {
    final db = await _databaseHelper.database;

    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.productsTable,
      where: 'category_id = ? AND is_active = 1',
      whereArgs: [categoryId],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// تحديث بيانات المنتج
  Future<int> updateProduct(Product product) async {
    final db = await _databaseHelper.database;

    // التحقق من عدم وجود باركود مكرر (إذا كان موجوداً)
    if (product.barcode != null && product.barcode!.isNotEmpty) {
      final existingProduct = await getProductByBarcode(product.barcode!);
      if (existingProduct != null && existingProduct.id != product.id) {
        throw Exception('الباركود موجود مسبقاً');
      }
    }

    final updatedProduct = product.copyWith(updatedAt: DateTime.now());

    return await db.update(
      AppConstants.productsTable,
      updatedProduct.toMap(),
      where: 'id = ?',
      whereArgs: [product.id],
    );
  }

  /// تحديث كمية المنتج
  Future<int> updateProductQuantity(
    int productId,
    int newQuantity,
    int userId,
    TransactionType type, {
    String? notes,
  }) async {
    final db = await _databaseHelper.database;

    return await db.transaction((txn) async {
      // الحصول على المنتج الحالي
      final productMaps = await txn.query(
        AppConstants.productsTable,
        where: 'id = ?',
        whereArgs: [productId],
      );

      if (productMaps.isEmpty) {
        throw Exception('المنتج غير موجود');
      }

      final currentProduct = Product.fromMap(productMaps.first);
      final previousQuantity = currentProduct.quantity;

      // تحديث كمية المنتج
      await txn.update(
        AppConstants.productsTable,
        {
          'quantity': newQuantity,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [productId],
      );

      // إضافة سجل العملية
      final transactionLog = TransactionLog(
        productId: productId,
        userId: userId,
        type: type,
        quantity: (newQuantity - previousQuantity).abs(),
        previousQuantity: previousQuantity,
        newQuantity: newQuantity,
        notes: notes,
        createdAt: DateTime.now(),
      );

      await txn.insert(
        AppConstants.transactionLogsTable,
        transactionLog.toMap(),
      );

      return 1;
    });
  }

  /// تفعيل/إلغاء تفعيل المنتج
  Future<int> toggleProductStatus(int productId, bool isActive) async {
    final db = await _databaseHelper.database;

    return await db.update(
      AppConstants.productsTable,
      {
        'is_active': isActive ? 1 : 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [productId],
    );
  }

  /// حذف المنتج
  Future<int> deleteProduct(int productId) async {
    final db = await _databaseHelper.database;

    return await db.transaction((txn) async {
      // حذف سجلات العمليات المرتبطة بالمنتج
      await txn.delete(
        AppConstants.transactionLogsTable,
        where: 'product_id = ?',
        whereArgs: [productId],
      );

      // حذف المنتج
      return await txn.delete(
        AppConstants.productsTable,
        where: 'id = ?',
        whereArgs: [productId],
      );
    });
  }

  /// البحث في المنتجات
  Future<List<Product>> searchProducts(String query) async {
    final db = await _databaseHelper.database;

    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.productsTable,
      where: 'name LIKE ? OR description LIKE ? OR barcode LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// البحث المتقدم في المنتجات
  Future<List<Product>> advancedSearch({
    String? name,
    int? categoryId,
    String? barcode,
    double? minPrice,
    double? maxPrice,
    StockStatus? stockStatus,
  }) async {
    final db = await _databaseHelper.database;

    List<String> whereConditions = ['is_active = 1'];
    List<dynamic> whereArgs = [];

    if (name != null && name.isNotEmpty) {
      whereConditions.add('name LIKE ?');
      whereArgs.add('%$name%');
    }

    if (categoryId != null) {
      whereConditions.add('category_id = ?');
      whereArgs.add(categoryId);
    }

    if (barcode != null && barcode.isNotEmpty) {
      whereConditions.add('barcode LIKE ?');
      whereArgs.add('%$barcode%');
    }

    if (minPrice != null) {
      whereConditions.add('selling_price >= ?');
      whereArgs.add(minPrice);
    }

    if (maxPrice != null) {
      whereConditions.add('selling_price <= ?');
      whereArgs.add(maxPrice);
    }

    if (stockStatus != null) {
      switch (stockStatus) {
        case StockStatus.outOfStock:
          whereConditions.add('quantity = 0');
          break;
        case StockStatus.lowStock:
          whereConditions.add('quantity > 0 AND quantity <= min_quantity');
          break;
        case StockStatus.inStock:
          whereConditions.add('quantity > min_quantity');
          break;
      }
    }

    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.productsTable,
      where: whereConditions.join(' AND '),
      whereArgs: whereArgs,
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// الحصول على المنتجات منخفضة المخزون
  Future<List<Product>> getLowStockProducts() async {
    final db = await _databaseHelper.database;

    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.productsTable,
      where: 'quantity <= min_quantity AND quantity > 0 AND is_active = 1',
      orderBy: 'quantity ASC',
    );

    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// الحصول على المنتجات نافدة المخزون
  Future<List<Product>> getOutOfStockProducts() async {
    final db = await _databaseHelper.database;

    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.productsTable,
      where: 'quantity = 0 AND is_active = 1',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// الحصول على أكثر المنتجات قيمة
  Future<List<Product>> getMostValuableProducts({int limit = 10}) async {
    final db = await _databaseHelper.database;

    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.productsTable,
      where: 'is_active = 1',
      orderBy: '(selling_price * quantity) DESC',
      limit: limit,
    );

    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// الحصول على المنتجات الأكثر ربحية
  Future<List<Product>> getMostProfitableProducts({int limit = 10}) async {
    final db = await _databaseHelper.database;

    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.productsTable,
      where: 'is_active = 1 AND purchase_price > 0',
      orderBy: '((selling_price - purchase_price) * quantity) DESC',
      limit: limit,
    );

    return List.generate(maps.length, (i) {
      return Product.fromMap(maps[i]);
    });
  }

  /// التحقق من صحة بيانات المنتج
  Map<String, String> validateProduct(Product product) {
    Map<String, String> errors = {};

    if (product.name.trim().isEmpty) {
      errors['name'] = 'اسم المنتج مطلوب';
    }

    if (product.purchasePrice < 0) {
      errors['purchase_price'] = 'سعر الشراء يجب أن يكون أكبر من أو يساوي صفر';
    }

    if (product.sellingPrice < 0) {
      errors['selling_price'] = 'سعر البيع يجب أن يكون أكبر من أو يساوي صفر';
    }

    if (product.sellingPrice < product.purchasePrice) {
      errors['selling_price'] = 'سعر البيع يجب أن يكون أكبر من سعر الشراء';
    }

    if (product.quantity < 0) {
      errors['quantity'] = 'الكمية يجب أن تكون أكبر من أو تساوي صفر';
    }

    if (product.minQuantity < 0) {
      errors['min_quantity'] =
          'الحد الأدنى للكمية يجب أن يكون أكبر من أو يساوي صفر';
    }

    return errors;
  }

  /// الحصول على إحصائيات المنتجات
  Future<Map<String, dynamic>> getProductStatistics() async {
    final db = await _databaseHelper.database;

    final totalProducts = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.productsTable} WHERE is_active = 1
    ''');

    final lowStockCount = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.productsTable} 
      WHERE quantity <= min_quantity AND quantity > 0 AND is_active = 1
    ''');

    final outOfStockCount = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.productsTable} 
      WHERE quantity = 0 AND is_active = 1
    ''');

    final totalValue = await db.rawQuery('''
      SELECT SUM(selling_price * quantity) as total FROM ${AppConstants.productsTable} 
      WHERE is_active = 1
    ''');

    final totalCost = await db.rawQuery('''
      SELECT SUM(purchase_price * quantity) as total FROM ${AppConstants.productsTable} 
      WHERE is_active = 1
    ''');

    return {
      'total_products': totalProducts.first['count'],
      'low_stock_count': lowStockCount.first['count'],
      'out_of_stock_count': outOfStockCount.first['count'],
      'total_value': totalValue.first['total'] ?? 0.0,
      'total_cost': totalCost.first['total'] ?? 0.0,
      'expected_profit':
          ((totalValue.first['total'] as double?) ?? 0.0) -
          ((totalCost.first['total'] as double?) ?? 0.0),
    };
  }
}
