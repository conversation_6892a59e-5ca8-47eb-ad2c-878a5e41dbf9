import '../database/database_helper.dart';
import '../models/models.dart';
import '../constants/app_constants.dart';

/// خدمة سجل العمليات
/// Transaction Log Service for tracking inventory operations
class TransactionLogService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إضافة سجل عملية جديد
  Future<int> addTransactionLog(TransactionLog log) async {
    final db = await _databaseHelper.database;
    return await db.insert(AppConstants.transactionLogsTable, log.toMap());
  }

  /// الحصول على سجل عملية بواسطة المعرف
  Future<TransactionLog?> getTransactionLogById(int id) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.transactionLogsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return TransactionLog.fromMap(maps.first);
    }
    
    return null;
  }

  /// الحصول على جميع سجلات العمليات
  Future<List<TransactionLog>> getAllTransactionLogs() async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.transactionLogsTable,
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return TransactionLog.fromMap(maps[i]);
    });
  }

  /// الحصول على سجلات العمليات للمنتج
  Future<List<TransactionLog>> getTransactionLogsByProduct(int productId) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.transactionLogsTable,
      where: 'product_id = ?',
      whereArgs: [productId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return TransactionLog.fromMap(maps[i]);
    });
  }

  /// الحصول على سجلات العمليات للمستخدم
  Future<List<TransactionLog>> getTransactionLogsByUser(int userId) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.transactionLogsTable,
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return TransactionLog.fromMap(maps[i]);
    });
  }

  /// الحصول على سجلات العمليات حسب النوع
  Future<List<TransactionLog>> getTransactionLogsByType(TransactionType type) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.transactionLogsTable,
      where: 'type = ?',
      whereArgs: [type.index],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return TransactionLog.fromMap(maps[i]);
    });
  }

  /// الحصول على سجلات العمليات في فترة زمنية
  Future<List<TransactionLog>> getTransactionLogsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.transactionLogsTable,
      where: 'created_at BETWEEN ? AND ?',
      whereArgs: [
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return TransactionLog.fromMap(maps[i]);
    });
  }

  /// الحصول على سجلات العمليات مع تفاصيل المنتج والمستخدم
  Future<List<Map<String, dynamic>>> getTransactionLogsWithDetails() async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        tl.*,
        p.name as product_name,
        p.barcode as product_barcode,
        u.full_name as user_name,
        c.name as category_name
      FROM ${AppConstants.transactionLogsTable} tl
      LEFT JOIN ${AppConstants.productsTable} p ON tl.product_id = p.id
      LEFT JOIN ${AppConstants.usersTable} u ON tl.user_id = u.id
      LEFT JOIN ${AppConstants.categoriesTable} c ON p.category_id = c.id
      ORDER BY tl.created_at DESC
    ''');

    return maps.map((map) {
      return {
        'transaction_log': TransactionLog.fromMap(map),
        'product_name': map['product_name'],
        'product_barcode': map['product_barcode'],
        'user_name': map['user_name'],
        'category_name': map['category_name'],
      };
    }).toList();
  }

  /// البحث في سجلات العمليات
  Future<List<Map<String, dynamic>>> searchTransactionLogs(String query) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        tl.*,
        p.name as product_name,
        p.barcode as product_barcode,
        u.full_name as user_name,
        c.name as category_name
      FROM ${AppConstants.transactionLogsTable} tl
      LEFT JOIN ${AppConstants.productsTable} p ON tl.product_id = p.id
      LEFT JOIN ${AppConstants.usersTable} u ON tl.user_id = u.id
      LEFT JOIN ${AppConstants.categoriesTable} c ON p.category_id = c.id
      WHERE p.name LIKE ? OR p.barcode LIKE ? OR u.full_name LIKE ? OR tl.notes LIKE ?
      ORDER BY tl.created_at DESC
    ''', ['%$query%', '%$query%', '%$query%', '%$query%']);

    return maps.map((map) {
      return {
        'transaction_log': TransactionLog.fromMap(map),
        'product_name': map['product_name'],
        'product_barcode': map['product_barcode'],
        'user_name': map['user_name'],
        'category_name': map['category_name'],
      };
    }).toList();
  }

  /// الحصول على إحصائيات العمليات
  Future<Map<String, dynamic>> getTransactionStatistics() async {
    final db = await _databaseHelper.database;
    
    // إجمالي العمليات
    final totalTransactions = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.transactionLogsTable}
    ''');
    
    // العمليات حسب النوع
    final transactionsByType = await db.rawQuery('''
      SELECT type, COUNT(*) as count 
      FROM ${AppConstants.transactionLogsTable} 
      GROUP BY type
    ''');
    
    // العمليات اليوم
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    final todayTransactions = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.transactionLogsTable}
      WHERE created_at BETWEEN ? AND ?
    ''', [startOfDay.toIso8601String(), endOfDay.toIso8601String()]);
    
    // العمليات هذا الأسبوع
    final startOfWeek = today.subtract(Duration(days: today.weekday - 1));
    final startOfWeekDay = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);
    
    final weekTransactions = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.transactionLogsTable}
      WHERE created_at >= ?
    ''', [startOfWeekDay.toIso8601String()]);
    
    // العمليات هذا الشهر
    final startOfMonth = DateTime(today.year, today.month, 1);
    
    final monthTransactions = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.transactionLogsTable}
      WHERE created_at >= ?
    ''', [startOfMonth.toIso8601String()]);
    
    Map<String, int> typeStats = {};
    for (var row in transactionsByType) {
      TransactionType type = TransactionType.values[row['type']];
      typeStats[type.displayName] = row['count'];
    }
    
    return {
      'total': totalTransactions.first['count'],
      'today': todayTransactions.first['count'],
      'this_week': weekTransactions.first['count'],
      'this_month': monthTransactions.first['count'],
      'by_type': typeStats,
    };
  }

  /// الحصول على أكثر المنتجات نشاطاً
  Future<List<Map<String, dynamic>>> getMostActiveProducts({int limit = 10}) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        p.id,
        p.name,
        p.barcode,
        COUNT(tl.id) as transaction_count
      FROM ${AppConstants.productsTable} p
      LEFT JOIN ${AppConstants.transactionLogsTable} tl ON p.id = tl.product_id
      WHERE p.is_active = 1
      GROUP BY p.id
      ORDER BY transaction_count DESC
      LIMIT ?
    ''', [limit]);

    return maps;
  }

  /// الحصول على أكثر المستخدمين نشاطاً
  Future<List<Map<String, dynamic>>> getMostActiveUsers({int limit = 10}) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        u.id,
        u.full_name,
        u.username,
        COUNT(tl.id) as transaction_count
      FROM ${AppConstants.usersTable} u
      LEFT JOIN ${AppConstants.transactionLogsTable} tl ON u.id = tl.user_id
      WHERE u.is_active = 1
      GROUP BY u.id
      ORDER BY transaction_count DESC
      LIMIT ?
    ''', [limit]);

    return maps;
  }

  /// الحصول على تقرير حركة المخزون
  Future<Map<String, dynamic>> getStockMovementReport(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    
    // العمليات الداخلة (تزيد المخزون)
    final stockInTransactions = await db.rawQuery('''
      SELECT 
        SUM(quantity) as total_quantity,
        COUNT(*) as transaction_count
      FROM ${AppConstants.transactionLogsTable}
      WHERE type IN (?, ?) AND created_at BETWEEN ? AND ?
    ''', [
      TransactionType.stockIn.index,
      TransactionType.return_.index,
      startDate.toIso8601String(),
      endDate.toIso8601String(),
    ]);
    
    // العمليات الخارجة (تقلل المخزون)
    final stockOutTransactions = await db.rawQuery('''
      SELECT 
        SUM(quantity) as total_quantity,
        COUNT(*) as transaction_count
      FROM ${AppConstants.transactionLogsTable}
      WHERE type IN (?, ?, ?, ?) AND created_at BETWEEN ? AND ?
    ''', [
      TransactionType.stockOut.index,
      TransactionType.sale.index,
      TransactionType.damaged.index,
      TransactionType.expired.index,
      startDate.toIso8601String(),
      endDate.toIso8601String(),
    ]);
    
    // التعديلات
    final adjustmentTransactions = await db.rawQuery('''
      SELECT 
        SUM(ABS(new_quantity - previous_quantity)) as total_quantity,
        COUNT(*) as transaction_count
      FROM ${AppConstants.transactionLogsTable}
      WHERE type = ? AND created_at BETWEEN ? AND ?
    ''', [
      TransactionType.adjustment.index,
      startDate.toIso8601String(),
      endDate.toIso8601String(),
    ]);
    
    return {
      'stock_in': {
        'quantity': stockInTransactions.first['total_quantity'] ?? 0,
        'transactions': stockInTransactions.first['transaction_count'] ?? 0,
      },
      'stock_out': {
        'quantity': stockOutTransactions.first['total_quantity'] ?? 0,
        'transactions': stockOutTransactions.first['transaction_count'] ?? 0,
      },
      'adjustments': {
        'quantity': adjustmentTransactions.first['total_quantity'] ?? 0,
        'transactions': adjustmentTransactions.first['transaction_count'] ?? 0,
      },
    };
  }

  /// حذف سجلات العمليات القديمة
  Future<int> deleteOldTransactionLogs(DateTime beforeDate) async {
    final db = await _databaseHelper.database;
    
    return await db.delete(
      AppConstants.transactionLogsTable,
      where: 'created_at < ?',
      whereArgs: [beforeDate.toIso8601String()],
    );
  }

  /// تصدير سجلات العمليات
  Future<List<Map<String, dynamic>>> exportTransactionLogs({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final db = await _databaseHelper.database;
    
    String whereClause = '';
    List<dynamic> whereArgs = [];
    
    if (startDate != null && endDate != null) {
      whereClause = 'WHERE tl.created_at BETWEEN ? AND ?';
      whereArgs = [startDate.toIso8601String(), endDate.toIso8601String()];
    }
    
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        tl.id,
        tl.type,
        tl.quantity,
        tl.previous_quantity,
        tl.new_quantity,
        tl.notes,
        tl.created_at,
        p.name as product_name,
        p.barcode as product_barcode,
        u.full_name as user_name,
        c.name as category_name
      FROM ${AppConstants.transactionLogsTable} tl
      LEFT JOIN ${AppConstants.productsTable} p ON tl.product_id = p.id
      LEFT JOIN ${AppConstants.usersTable} u ON tl.user_id = u.id
      LEFT JOIN ${AppConstants.categoriesTable} c ON p.category_id = c.id
      $whereClause
      ORDER BY tl.created_at DESC
    ''', whereArgs);

    return maps.map((map) {
      return {
        'معرف العملية': map['id'],
        'نوع العملية': TransactionType.values[map['type']].displayName,
        'اسم المنتج': map['product_name'],
        'الباركود': map['product_barcode'],
        'الفئة': map['category_name'],
        'الكمية': map['quantity'],
        'الكمية السابقة': map['previous_quantity'],
        'الكمية الجديدة': map['new_quantity'],
        'المستخدم': map['user_name'],
        'ملاحظات': map['notes'],
        'تاريخ العملية': map['created_at'],
      };
    }).toList();
  }
}
