import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_constants.dart';
import '../../l10n/app_localizations.dart';
import '../../providers/auth_provider.dart';
import '../../providers/settings_provider.dart';
import '../admin/user_management_screen.dart';
import '../auth/simple_login_screen.dart';

/// شاشة الإعدادات
/// Settings Screen
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthProvider, SettingsProvider>(
      builder: (context, authProvider, settingsProvider, child) {
        final user = authProvider.currentUser;
        final isAdmin = user?.role.name == 'admin';

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات المستخدم
              _buildUserInfo(user, isAdmin),
              const SizedBox(height: 20),

              // إعدادات التطبيق
              _buildAppSettings(),
              const SizedBox(height: 20),

              // إعدادات المخزون
              _buildInventorySettings(isAdmin),
              const SizedBox(height: 20),

              // إعدادات النظام (للمدير فقط)
              if (isAdmin) ...[
                _buildSystemSettings(),
                const SizedBox(height: 20),
              ],

              // معلومات التطبيق
              _buildAppInfo(),
              const SizedBox(height: 20),

              // إجراءات الحساب
              _buildAccountActions(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildUserInfo(user, bool isAdmin) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.blue.withValues(alpha: 0.1),
                  child: Icon(
                    isAdmin ? Icons.admin_panel_settings : Icons.person,
                    color: Colors.blue,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user?.fullName ?? user?.username ?? 'مستخدم',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'اسم المستخدم: ${user?.username ?? 'غير محدد'}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(top: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: isAdmin
                              ? Colors.red.withValues(alpha: 0.1)
                              : Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          isAdmin ? 'مدير النظام' : 'موظف المخزن',
                          style: TextStyle(
                            fontSize: 12,
                            color: isAdmin ? Colors.red : Colors.blue,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.settings, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'إعدادات التطبيق',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Consumer<SettingsProvider>(
              builder: (context, settingsProvider, child) {
                return Column(
                  children: [
                    _buildSettingItem(
                      AppLocalizations.of(context)!.currency,
                      '${settingsProvider.currencySymbol} - ${settingsProvider.getCurrencyName()}',
                      Icons.attach_money,
                      () => _showCurrencyDialog(settingsProvider),
                    ),
                    _buildSettingItem(
                      AppLocalizations.of(context)!.language,
                      settingsProvider.getLanguageName(),
                      Icons.language,
                      () => _showLanguageDialog(settingsProvider),
                    ),
                    _buildSettingItem(
                      AppLocalizations.of(context)!.theme,
                      settingsProvider.getThemeName(),
                      Icons.brightness_6,
                      () => _showThemeDialog(settingsProvider),
                    ),
                    _buildSettingItem(
                      AppLocalizations.of(context)!.notifications,
                      'مفعلة',
                      Icons.notifications,
                      () => _showNotificationSettings(),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInventorySettings(bool isAdmin) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.inventory_2, color: Colors.green),
                SizedBox(width: 8),
                Text(
                  'إعدادات المخزون',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildSettingItem(
              'الحد الأدنى للتنبيه',
              '5 قطع',
              Icons.warning,
              isAdmin ? () => _showMinStockDialog() : null,
            ),
            _buildSettingItem(
              'تنبيهات المخزون',
              'مفعلة',
              Icons.notifications_active,
              () => _showStockAlertsDialog(),
            ),
            _buildSettingItem(
              'تحديث تلقائي',
              'كل ساعة',
              Icons.refresh,
              isAdmin ? () => _showAutoUpdateDialog() : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.admin_panel_settings, color: Colors.red),
                SizedBox(width: 8),
                Text(
                  'إعدادات النظام',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildSettingItem(
              'إدارة المستخدمين',
              'عرض وتعديل',
              Icons.people,
              () => _showUserManagement(),
            ),
            _buildSettingItem(
              'النسخ الاحتياطي',
              'يومي',
              Icons.backup,
              () => _showBackupSettings(),
            ),
            _buildSettingItem(
              'سجل النشاطات',
              'عرض السجل',
              Icons.history,
              () => _showActivityLog(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.info, color: Colors.purple),
                SizedBox(width: 8),
                Text(
                  'معلومات التطبيق',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildInfoItem('اسم التطبيق', AppConstants.appName),
            _buildInfoItem('الإصدار', '1.0.0'),
            _buildInfoItem('تاريخ البناء', '2025-06-06'),
            _buildInfoItem('المطور', 'فريق التطوير'),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountActions(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.account_circle, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'إجراءات الحساب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _changePassword(context),
                icon: const Icon(Icons.lock),
                label: const Text('تغيير كلمة المرور'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 12),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _logout(context),
                icon: const Icon(Icons.logout),
                label: const Text('تسجيل الخروج'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem(
    String title,
    String value,
    IconData icon,
    VoidCallback? onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.grey[600]),
      title: Text(title),
      subtitle: Text(value),
      trailing: onTap != null
          ? const Icon(Icons.arrow_forward_ios, size: 16)
          : null,
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildInfoItem(String title, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
          Text(value, style: const TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  // Dialog methods
  void _showCurrencyDialog(SettingsProvider settingsProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار العملة'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: AppConstants.supportedCurrencies.length,
            itemBuilder: (context, index) {
              final entry = AppConstants.supportedCurrencies.entries.elementAt(
                index,
              );
              final symbol = entry.key;
              final name = entry.value;
              final isSelected = settingsProvider.currencySymbol == symbol;

              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: isSelected
                      ? Colors.blue.withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.1),
                  child: Text(
                    symbol,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.blue : Colors.grey[600],
                    ),
                  ),
                ),
                title: Text(name),
                subtitle: Text('الرمز: $symbol'),
                trailing: isSelected
                    ? const Icon(Icons.check, color: Colors.green)
                    : null,
                onTap: () async {
                  final navigator = Navigator.of(context);
                  await settingsProvider.setCurrencySymbol(symbol);
                  if (mounted) {
                    navigator.pop();
                    _showSnackBar('تم تغيير العملة إلى $name');
                  }
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog(SettingsProvider settingsProvider) {
    final l10n = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.language),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.language),
              title: Text(l10n.arabic),
              trailing: settingsProvider.language == 'ar'
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () async {
                Navigator.of(context).pop();
                await settingsProvider.setLanguage('ar');
                _showSnackBar('تم اختيار اللغة العربية');
              },
            ),
            ListTile(
              leading: const Icon(Icons.language),
              title: Text(l10n.english),
              trailing: settingsProvider.language == 'en'
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () async {
                Navigator.of(context).pop();
                await settingsProvider.setLanguage('en');
                _showSnackBar('English language selected');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
        ],
      ),
    );
  }

  void _showThemeDialog(SettingsProvider settingsProvider) {
    final l10n = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.theme),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.light_mode),
              title: Text(l10n.lightTheme),
              trailing: settingsProvider.theme == 'light'
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () async {
                Navigator.of(context).pop();
                await settingsProvider.setTheme('light');
                _showSnackBar(
                  settingsProvider.language == 'ar'
                      ? 'تم اختيار المظهر الفاتح'
                      : 'Light theme selected',
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.dark_mode),
              title: Text(l10n.darkTheme),
              trailing: settingsProvider.theme == 'dark'
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () async {
                Navigator.of(context).pop();
                await settingsProvider.setTheme('dark');
                _showSnackBar(
                  settingsProvider.language == 'ar'
                      ? 'تم اختيار المظهر الداكن'
                      : 'Dark theme selected',
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.brightness_auto),
              title: const Text('System'),
              trailing: settingsProvider.theme == 'system'
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
              onTap: () async {
                Navigator.of(context).pop();
                await settingsProvider.setTheme('system');
                _showSnackBar(
                  settingsProvider.language == 'ar'
                      ? 'تم اختيار المظهر حسب النظام'
                      : 'System theme selected',
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.auto_mode),
              title: const Text('تلقائي (حسب النظام)'),
              onTap: () {
                Navigator.of(context).pop();
                _showSnackBar('تم اختيار المظهر التلقائي');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات الإشعارات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('إشعارات المخزون'),
              subtitle: const Text('تنبيهات عند نفاد المخزون'),
              value: true,
              onChanged: (value) {
                Navigator.of(context).pop();
                _showSnackBar(
                  value
                      ? 'تم تفعيل إشعارات المخزون'
                      : 'تم إلغاء إشعارات المخزون',
                );
              },
            ),
            SwitchListTile(
              title: const Text('إشعارات التحديثات'),
              subtitle: const Text('تنبيهات عند توفر تحديثات'),
              value: false,
              onChanged: (value) {
                Navigator.of(context).pop();
                _showSnackBar(
                  value
                      ? 'تم تفعيل إشعارات التحديثات'
                      : 'تم إلغاء إشعارات التحديثات',
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showMinStockDialog() {
    final TextEditingController controller = TextEditingController(text: '5');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الحد الأدنى للتنبيه'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('أدخل الحد الأدنى لكمية المنتج للتنبيه:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'الكمية',
                border: OutlineInputBorder(),
                suffixText: 'قطعة',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showSnackBar('تم تحديث الحد الأدنى إلى ${controller.text} قطعة');
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _showStockAlertsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تنبيهات المخزون'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('تنبيه نفاد المخزون'),
              subtitle: const Text('إشعار عند نفاد المنتج'),
              value: true,
              onChanged: (value) {},
            ),
            SwitchListTile(
              title: const Text('تنبيه الكمية القليلة'),
              subtitle: const Text('إشعار عند انخفاض الكمية'),
              value: true,
              onChanged: (value) {},
            ),
            SwitchListTile(
              title: const Text('تنبيه يومي'),
              subtitle: const Text('تقرير يومي عن حالة المخزون'),
              value: false,
              onChanged: (value) {},
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showAutoUpdateDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('التحديث التلقائي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('كل 15 دقيقة'),
              leading: Radio<String>(
                value: '15min',
                groupValue: 'hour',
                onChanged: (value) {
                  Navigator.of(context).pop();
                  _showSnackBar('تم تعيين التحديث كل 15 دقيقة');
                },
              ),
            ),
            ListTile(
              title: const Text('كل ساعة'),
              leading: Radio<String>(
                value: 'hour',
                groupValue: 'hour',
                onChanged: (value) {
                  Navigator.of(context).pop();
                  _showSnackBar('تم تعيين التحديث كل ساعة');
                },
              ),
            ),
            ListTile(
              title: const Text('يدوي فقط'),
              leading: Radio<String>(
                value: 'manual',
                groupValue: 'hour',
                onChanged: (value) {
                  Navigator.of(context).pop();
                  _showSnackBar('تم تعيين التحديث اليدوي فقط');
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showUserManagement() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const UserManagementScreen()),
    );
  }

  void _showBackupSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات النسخ الاحتياطي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('نسخة احتياطية يدوية'),
              subtitle: const Text('إنشاء نسخة احتياطية الآن'),
              leading: const Icon(Icons.backup),
              onTap: () {
                Navigator.of(context).pop();
                _showSnackBar('جاري إنشاء النسخة الاحتياطية...');
              },
            ),
            const Divider(),
            SwitchListTile(
              title: const Text('نسخ احتياطي تلقائي'),
              subtitle: const Text('نسخة احتياطية يومية'),
              value: true,
              onChanged: (value) {
                _showSnackBar(
                  value ? 'تم تفعيل النسخ التلقائي' : 'تم إلغاء النسخ التلقائي',
                );
              },
            ),
            ListTile(
              title: const Text('استعادة من نسخة احتياطية'),
              subtitle: const Text('استعادة البيانات'),
              leading: const Icon(Icons.restore),
              onTap: () {
                Navigator.of(context).pop();
                _showRestoreDialog();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showRestoreDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('استعادة النسخة الاحتياطية'),
        content: const Text(
          'هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showSnackBar('جاري استعادة النسخة الاحتياطية...');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('استعادة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showActivityLog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سجل النشاطات'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView(
            children: [
              _buildLogItem('تسجيل دخول', 'admin', '2025-06-06 20:30'),
              _buildLogItem('إضافة منتج', 'employee', '2025-06-06 19:45'),
              _buildLogItem('تحديث كمية', 'employee', '2025-06-06 18:20'),
              _buildLogItem('حذف منتج', 'admin', '2025-06-06 17:15'),
              _buildLogItem('تسجيل خروج', 'employee', '2025-06-06 16:30'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showSnackBar('تم تصدير سجل النشاطات');
            },
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  Widget _buildLogItem(String action, String user, String time) {
    return ListTile(
      leading: const Icon(Icons.history),
      title: Text(action),
      subtitle: Text('المستخدم: $user'),
      trailing: Text(
        time,
        style: const TextStyle(fontSize: 12, color: Colors.grey),
      ),
    );
  }

  void _changePassword(BuildContext context) {
    final TextEditingController currentPasswordController =
        TextEditingController();
    final TextEditingController newPasswordController = TextEditingController();
    final TextEditingController confirmPasswordController =
        TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير كلمة المرور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: currentPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور الحالية',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: newPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور الجديدة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock_outline),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: confirmPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'تأكيد كلمة المرور الجديدة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.lock_outline),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (newPasswordController.text !=
                  confirmPasswordController.text) {
                _showSnackBar('كلمة المرور الجديدة غير متطابقة');
                return;
              }
              if (newPasswordController.text.length < 6) {
                _showSnackBar('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
              }
              Navigator.of(context).pop();
              _showSnackBar('تم تغيير كلمة المرور بنجاح');
            },
            child: const Text('تغيير'),
          ),
        ],
      ),
    );
  }

  void _logout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Provider.of<AuthProvider>(context, listen: false).logout();
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const SimpleLoginScreen(),
                ),
              );
            },
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  // Helper method to show snackbar
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
