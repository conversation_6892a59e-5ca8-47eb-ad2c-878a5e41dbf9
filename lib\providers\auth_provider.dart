import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants/app_constants.dart';
import '../models/models.dart';
import '../services/services.dart';

/// مزود المصادقة
/// Authentication Provider for managing user authentication state
class AuthProvider with ChangeNotifier {
  final UserService _userService = UserService();
  final PermissionService _permissionService = PermissionService();

  User? _currentUser;
  final List<User> _allUsers = [];
  final List<Permission> _userPermissions = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  User? get currentUser => _currentUser;
  List<User> get allUsers => _allUsers;
  List<Permission> get userPermissions => _userPermissions;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _currentUser != null;
  bool get isAdmin => _currentUser?.role == UserRole.admin;
  bool get isEmployee => _currentUser?.role == UserRole.employee;

  /// تسجيل الدخول
  Future<bool> login(String username, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final user = await _userService.login(username, password);

      if (user != null) {
        _currentUser = user;
        await _loadUserPermissions(user.id!);
        await _saveUserToPreferences(user);
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('اسم المستخدم أو كلمة المرور غير صحيحة');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء تسجيل الدخول: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    _currentUser = null;
    _userPermissions.clear();
    await _removeUserFromPreferences();
    notifyListeners();
  }

  /// التحقق من حالة تسجيل الدخول المحفوظة
  Future<void> checkAuthStatus() async {
    _setLoading(true);

    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(AppConstants.currentUserKey);

      if (userJson != null) {
        final userMap = json.decode(userJson);
        _currentUser = User.fromMap(userMap);

        // التحقق من صحة المستخدم في قاعدة البيانات
        final dbUser = await _userService.getUserById(_currentUser!.id!);
        if (dbUser == null || !dbUser.isActive) {
          await logout();
        } else {
          _currentUser = dbUser;
          await _loadUserPermissions(_currentUser!.id!);
        }
      }
    } catch (e) {
      await logout();
    }

    _setLoading(false);
    notifyListeners();
  }

  /// تحديث بيانات المستخدم الحالي
  Future<bool> updateCurrentUser(User updatedUser) async {
    _setLoading(true);
    _clearError();

    try {
      await _userService.updateUser(updatedUser);
      _currentUser = updatedUser;
      await _saveUserToPreferences(updatedUser);
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء تحديث البيانات: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// تغيير كلمة المرور
  Future<bool> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    _clearError();

    try {
      // التحقق من كلمة المرور الحالية
      final user = await _userService.login(
        _currentUser!.username,
        currentPassword,
      );
      if (user == null) {
        _setError('كلمة المرور الحالية غير صحيحة');
        _setLoading(false);
        return false;
      }

      // تحديث كلمة المرور
      await _userService.updatePassword(_currentUser!.id!, newPassword);

      // تحديث المستخدم الحالي
      _currentUser = _currentUser!.copyWith(password: newPassword);
      await _saveUserToPreferences(_currentUser!);

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء تغيير كلمة المرور: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// تحميل صلاحيات المستخدم
  Future<void> _loadUserPermissions(int userId) async {
    try {
      _userPermissions.clear();
      final permissions = await _permissionService.getUserPermissions(userId);
      _userPermissions.addAll(permissions);
    } catch (e) {
      print('Error loading user permissions: $e');
    }
  }

  /// التحقق من الصلاحيات
  bool hasPermission(String permission) {
    if (_currentUser == null) return false;

    // إذا كان المستخدم مدير، فله جميع الصلاحيات
    if (_currentUser!.role == UserRole.admin) return true;

    // التحقق من الصلاحيات المحددة
    return _userPermissions.any((p) => p.name == permission);
  }

  /// التحقق من الصلاحيات المتعددة
  bool hasAnyPermission(List<String> permissions) {
    return permissions.any((permission) => hasPermission(permission));
  }

  /// التحقق من جميع الصلاحيات
  bool hasAllPermissions(List<String> permissions) {
    return permissions.every((permission) => hasPermission(permission));
  }

  /// إعادة تحميل صلاحيات المستخدم الحالي
  Future<void> reloadUserPermissions() async {
    if (_currentUser != null) {
      await _loadUserPermissions(_currentUser!.id!);
      notifyListeners();
    }
  }

  /// التحقق من صحة بيانات تسجيل الدخول
  Map<String, String> validateLoginData(String username, String password) {
    Map<String, String> errors = {};

    if (username.trim().isEmpty) {
      errors['username'] = 'اسم المستخدم مطلوب';
    }

    if (password.trim().isEmpty) {
      errors['password'] = 'كلمة المرور مطلوبة';
    }

    return errors;
  }

  /// التحقق من صحة بيانات تغيير كلمة المرور
  Map<String, String> validatePasswordChange(
    String currentPassword,
    String newPassword,
    String confirmPassword,
  ) {
    Map<String, String> errors = {};

    if (currentPassword.trim().isEmpty) {
      errors['current_password'] = 'كلمة المرور الحالية مطلوبة';
    }

    if (newPassword.trim().isEmpty) {
      errors['new_password'] = 'كلمة المرور الجديدة مطلوبة';
    } else if (!_userService.validatePassword(newPassword)) {
      errors['new_password'] = 'كلمة المرور يجب أن تكون على الأقل 6 أحرف';
    }

    if (confirmPassword.trim().isEmpty) {
      errors['confirm_password'] = 'تأكيد كلمة المرور مطلوب';
    } else if (newPassword != confirmPassword) {
      errors['confirm_password'] = 'كلمات المرور غير متطابقة';
    }

    if (currentPassword == newPassword) {
      errors['new_password'] =
          'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية';
    }

    return errors;
  }

  /// حفظ بيانات المستخدم في التخزين المحلي
  Future<void> _saveUserToPreferences(User user) async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = json.encode(user.toMap());
    await prefs.setString(AppConstants.currentUserKey, userJson);
    await prefs.setBool(AppConstants.isLoggedInKey, true);
  }

  /// إزالة بيانات المستخدم من التخزين المحلي
  Future<void> _removeUserFromPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.currentUserKey);
    await prefs.setBool(AppConstants.isLoggedInKey, false);
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// إعادة تعيين حالة المزود
  void reset() {
    _currentUser = null;
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }

  /// الحصول على اسم المستخدم المعروض
  String get displayName {
    if (_currentUser == null) return '';
    return _currentUser!.fullName.isNotEmpty
        ? _currentUser!.fullName
        : _currentUser!.username;
  }

  /// الحصول على دور المستخدم المعروض
  String get displayRole {
    if (_currentUser == null) return '';
    return _currentUser!.role.displayName;
  }

  /// التحقق من انتهاء صلاحية الجلسة
  bool get isSessionExpired {
    if (_currentUser?.lastLogin == null) return false;

    final now = DateTime.now();
    final lastLogin = _currentUser!.lastLogin!;
    final sessionDuration = now.difference(lastLogin);

    return sessionDuration.inMinutes > AppConstants.sessionTimeoutMinutes;
  }

  /// تجديد الجلسة
  Future<void> refreshSession() async {
    if (_currentUser != null) {
      await _userService.updateLastLogin(_currentUser!.id!);
      _currentUser = _currentUser!.copyWith(lastLogin: DateTime.now());
      await _saveUserToPreferences(_currentUser!);
      notifyListeners();
    }
  }

  /// تحميل جميع المستخدمين (للمدير فقط)
  Future<void> loadAllUsers() async {
    if (!hasPermission('manage_users')) return;

    try {
      final users = await _userService.getAllUsers();
      _allUsers.clear();
      _allUsers.addAll(users);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل المستخدمين: ${e.toString()}');
    }
  }

  /// إنشاء مستخدم جديد
  Future<bool> createUser(
    String username,
    String password,
    String role,
    String? fullName,
  ) async {
    if (!hasPermission('manage_users')) return false;

    try {
      final user = User(
        username: username,
        password: password,
        role: UserRoleExtension.fromString(role),
        fullName: fullName ?? '',
        isActive: true,
        createdAt: DateTime.now(),
        lastLogin: null,
      );

      final userId = await _userService.createUser(user);
      if (userId > 0) {
        final newUser = user.copyWith(id: userId);
        _allUsers.add(newUser);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError('خطأ في إنشاء المستخدم: ${e.toString()}');
      return false;
    }
  }

  /// تحديث مستخدم
  Future<bool> updateUser(
    int userId,
    String username,
    String? fullName,
    String role,
    String? password,
  ) async {
    if (!hasPermission('manage_users')) return false;

    try {
      final userIndex = _allUsers.indexWhere((user) => user.id == userId);
      if (userIndex == -1) return false;

      final existingUser = _allUsers[userIndex];
      final updatedUser = existingUser.copyWith(
        username: username,
        fullName: fullName,
        role: UserRoleExtension.fromString(role),
        password: password ?? existingUser.password,
      );

      final rowsAffected = await _userService.updateUser(updatedUser);
      if (rowsAffected > 0) {
        _allUsers[userIndex] = updatedUser;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError('خطأ في تحديث المستخدم: ${e.toString()}');
      return false;
    }
  }

  /// إعادة تعيين كلمة مرور مستخدم
  Future<bool> resetUserPassword(int userId, String newPassword) async {
    if (!hasPermission('manage_users')) return false;

    try {
      final rowsAffected = await _userService.updatePassword(
        userId,
        newPassword,
      );
      if (rowsAffected > 0) {
        final userIndex = _allUsers.indexWhere((user) => user.id == userId);
        if (userIndex != -1) {
          _allUsers[userIndex] = _allUsers[userIndex].copyWith(
            password: newPassword,
          );
          notifyListeners();
        }
        return true;
      }
      return false;
    } catch (e) {
      _setError('خطأ في إعادة تعيين كلمة المرور: ${e.toString()}');
      return false;
    }
  }

  /// حذف مستخدم
  Future<bool> deleteUser(int userId) async {
    if (!hasPermission('manage_users')) return false;
    if (_currentUser?.id == userId) return false; // لا يمكن حذف المستخدم الحالي

    try {
      final rowsAffected = await _userService.deleteUser(userId);
      if (rowsAffected > 0) {
        _allUsers.removeWhere((user) => user.id == userId);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError('خطأ في حذف المستخدم: ${e.toString()}');
      return false;
    }
  }
}
