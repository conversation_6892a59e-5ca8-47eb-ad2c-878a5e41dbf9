# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Database files
*.db
*.sqlite
*.sqlite3

# Generated files
*.g.dart
*.freezed.dart
*.mocks.dart

# Coverage
coverage/
lcov.info

# Environment files
.env
.env.local
.env.production

# Temporary files
*.tmp
*.temp

# Logs
logs/
*.log

# OS generated files
Thumbs.db
ehthumbs.db

# IDE files
.vscode/settings.json
.vscode/launch.json

# Firebase
google-services.json
GoogleService-Info.plist
firebase_options.dart

# Keys and certificates
*.keystore
*.jks
*.p12
*.key
*.pem

# App specific
exports/
backups/
uploads/
