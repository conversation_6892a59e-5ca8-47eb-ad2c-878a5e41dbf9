{"dart.flutterSdkPath": null, "dart.lineLength": 80, "dart.insertArgumentPlaceholders": false, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.closingLabels": true, "dart.flutterOutline": true, "dart.flutterCreateAndroidLanguage": "kotlin", "dart.flutterCreateIOSLanguage": "swift", "dart.flutterCreatePlatforms": ["android", "ios"], "dart.flutterCreateOffline": false, "dart.flutterCreateOrganization": "com.stockapp", "dart.analysisServerFolding": true, "dart.showTodos": true, "dart.runPubGetOnPubspecChanges": true, "dart.warnWhenEditingFilesOutsideWorkspace": true, "dart.allowAnalytics": false, "dart.checkForSdkUpdates": true, "dart.promptToGetPackages": true, "dart.promptToRunIfErrors": true, "dart.buildRunnerAdditionalArgs": [], "dart.flutterScreenshotPath": "screenshots", "dart.maxLogLineLength": 2000, "dart.showInspectorNotificationsForWidgetErrors": true, "dart.previewCommitCharacters": true, "dart.enableSdkFormatter": true, "dart.enableServerSnippets": true, "dart.includeDependenciesInWorkspaceSymbols": true, "dart.normalizeWindowsDriveLetters": true, "dart.renameFilesWithClasses": "prompt", "dart.updateImportsOnRename": true, "dart.showMainCodeLens": true, "dart.showTestCodeLens": true, "dart.debugSdkLibraries": false, "dart.debugExternalPackageLibraries": false, "dart.evaluateGettersInDebugViews": true, "dart.evaluateToStringInDebugViews": true, "dart.flutterTrackWidgetCreation": true, "dart.hotReloadOnSave": "always", "dart.flutterHotReloadOnSave": "always", "dart.flutterHotRestartOnSave": "never", "dart.openDevTools": "flutter", "dart.flutterSelectDeviceWhenConnected": true, "dart.shareDevToolsWithFlutterSurvey": false, "dart.embedDevTools": true, "dart.devToolsTheme": "dark", "dart.devToolsLocation": "beside", "dart.vmAdditionalArgs": [], "dart.flutterAdditionalArgs": [], "dart.flutterRunAdditionalArgs": [], "dart.flutterTestAdditionalArgs": [], "dart.env": {}, "dart.flutterDaemonLogFile": null, "dart.observatoryLogFile": null, "dart.extensionLogFile": null, "dart.analyzerLogFile": null, "dart.analyzerDiagnosticsPort": null, "dart.analyzerInstrumentationLogFile": null, "dart.flutterStructuredErrors": true, "dart.showSkippedTests": true, "dart.reportAnalyzerErrors": true, "dart.allowTestsOutsideTestFolder": false, "dart.doNotFormat": [], "dart.enableCompletionCommitCharacters": false, "dart.triggerSignatureHelpAutomatically": true, "dart.documentation": "full", "dart.analysisExcludedFolders": ["build", ".dart_tool"], "editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": false, "editor.codeActionsOnSave": {"source.fixAll": true, "source.organizeImports": true}, "files.associations": {"*.dart": "dart"}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/.dart_tool": true, "**/build": true, "**/.flutter-plugins": true, "**/.flutter-plugins-dependencies": true, "**/.packages": true, "**/.pub-cache": true, "**/.pub": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/*.code-search": true, "**/build": true, "**/.dart_tool": true, "**/.pub-cache": true}, "emmet.includeLanguages": {"dart": "html"}, "git.ignoreLimitWarning": true, "todo-tree.general.tags": ["BUG", "HACK", "FIXME", "TODO", "XXX", "[ ]", "[x]"], "todo-tree.regex.regex": "(//|#|<!--|;|/\\*|^|^\\s*(-|\\*|\\+|\\d+\\.)\\s*)($TAGS)", "bracketPairColorizer.depreciation-notice": false}