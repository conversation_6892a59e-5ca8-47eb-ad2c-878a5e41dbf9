// اختبارات تطبيق إدارة المخازن
// Stock Management App Widget Tests

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:stock_management_app/main.dart';
import 'package:stock_management_app/providers/providers.dart';
import 'package:stock_management_app/screens/auth/login_screen.dart';

void main() {
  group('Stock Management App Tests', () {
    testWidgets('App should show splash screen initially', (
      WidgetTester tester,
    ) async {
      // بناء التطبيق وتشغيل إطار
      await tester.pumpWidget(const StockManagementApp());

      // التحقق من ظهور شاشة البداية
      expect(find.text('نظام إدارة المخازن'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('Login screen should have required fields', (
      WidgetTester tester,
    ) async {
      // بناء شاشة تسجيل الدخول
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => AuthProvider()),
            ChangeNotifierProvider(create: (_) => ProductProvider()),
            ChangeNotifierProvider(create: (_) => CategoryProvider()),
          ],
          child: const MaterialApp(home: LoginScreen()),
        ),
      );

      // التحقق من وجود حقول تسجيل الدخول
      expect(find.text('اسم المستخدم'), findsOneWidget);
      expect(find.text('كلمة المرور'), findsOneWidget);
      expect(find.text('تسجيل الدخول'), findsOneWidget);
      expect(find.text('تذكرني'), findsOneWidget);
    });

    testWidgets('Login form validation should work', (
      WidgetTester tester,
    ) async {
      // بناء شاشة تسجيل الدخول
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => AuthProvider()),
            ChangeNotifierProvider(create: (_) => ProductProvider()),
            ChangeNotifierProvider(create: (_) => CategoryProvider()),
          ],
          child: const MaterialApp(home: LoginScreen()),
        ),
      );

      // محاولة تسجيل الدخول بدون بيانات
      await tester.tap(find.text('تسجيل الدخول'));
      await tester.pump();

      // التحقق من ظهور رسائل الخطأ
      expect(find.text('هذا الحقل مطلوب'), findsAtLeastNWidgets(2));
    });

    testWidgets('Username field should accept text input', (
      WidgetTester tester,
    ) async {
      // بناء شاشة تسجيل الدخول
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => AuthProvider()),
            ChangeNotifierProvider(create: (_) => ProductProvider()),
            ChangeNotifierProvider(create: (_) => CategoryProvider()),
          ],
          child: const MaterialApp(home: LoginScreen()),
        ),
      );

      // العثور على حقل اسم المستخدم وإدخال نص
      final usernameField = find.byType(TextFormField).first;
      await tester.enterText(usernameField, 'admin');
      await tester.pump();

      // التحقق من إدخال النص
      expect(find.text('admin'), findsOneWidget);
    });

    testWidgets('Password field should be obscured', (
      WidgetTester tester,
    ) async {
      // بناء شاشة تسجيل الدخول
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => AuthProvider()),
            ChangeNotifierProvider(create: (_) => ProductProvider()),
            ChangeNotifierProvider(create: (_) => CategoryProvider()),
          ],
          child: const MaterialApp(home: LoginScreen()),
        ),
      );

      // البحث عن أيقونة إخفاء/إظهار كلمة المرور
      final visibilityIcon = find.byIcon(Icons.visibility);
      expect(visibilityIcon, findsOneWidget);
    });

    testWidgets('Remember me checkbox should be toggleable', (
      WidgetTester tester,
    ) async {
      // بناء شاشة تسجيل الدخول
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => AuthProvider()),
            ChangeNotifierProvider(create: (_) => ProductProvider()),
            ChangeNotifierProvider(create: (_) => CategoryProvider()),
          ],
          child: const MaterialApp(home: LoginScreen()),
        ),
      );

      // العثور على مربع الاختيار "تذكرني"
      final checkbox = find.byType(Checkbox);
      expect(checkbox, findsOneWidget);

      // التحقق من أن المربع غير محدد في البداية
      Checkbox checkboxWidget = tester.widget(checkbox);
      expect(checkboxWidget.value, isFalse);

      // النقر على المربع
      await tester.tap(checkbox);
      await tester.pump();

      // التحقق من تحديد المربع
      checkboxWidget = tester.widget(checkbox);
      expect(checkboxWidget.value, isTrue);
    });
  });
}
