/// نموذج سجل العمليات
/// Transaction Log Model for tracking inventory operations
class TransactionLog {
  final int? id;
  final int productId;
  final int userId;
  final TransactionType type;
  final int quantity;
  final int previousQuantity;
  final int newQuantity;
  final String? notes;
  final DateTime createdAt;

  TransactionLog({
    this.id,
    required this.productId,
    required this.userId,
    required this.type,
    required this.quantity,
    required this.previousQuantity,
    required this.newQuantity,
    this.notes,
    required this.createdAt,
  });

  /// تحويل من Map إلى TransactionLog object
  factory TransactionLog.fromMap(Map<String, dynamic> map) {
    return TransactionLog(
      id: map['id'],
      productId: map['product_id'],
      userId: map['user_id'],
      type: TransactionType.values[map['type']],
      quantity: map['quantity'],
      previousQuantity: map['previous_quantity'],
      newQuantity: map['new_quantity'],
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  /// تحويل من TransactionLog object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'user_id': userId,
      'type': type.index,
      'quantity': quantity,
      'previous_quantity': previousQuantity,
      'new_quantity': newQuantity,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من سجل العملية
  TransactionLog copyWith({
    int? id,
    int? productId,
    int? userId,
    TransactionType? type,
    int? quantity,
    int? previousQuantity,
    int? newQuantity,
    String? notes,
    DateTime? createdAt,
  }) {
    return TransactionLog(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      quantity: quantity ?? this.quantity,
      previousQuantity: previousQuantity ?? this.previousQuantity,
      newQuantity: newQuantity ?? this.newQuantity,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'TransactionLog{id: $id, productId: $productId, type: $type, quantity: $quantity}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransactionLog &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// أنواع العمليات
enum TransactionType {
  stockIn,     // إدخال مخزون
  stockOut,    // إخراج مخزون
  adjustment,  // تعديل مخزون
  sale,        // بيع
  return_,     // إرجاع
  damaged,     // تالف
  expired,     // منتهي الصلاحية
}

extension TransactionTypeExtension on TransactionType {
  String get displayName {
    switch (this) {
      case TransactionType.stockIn:
        return 'إدخال مخزون';
      case TransactionType.stockOut:
        return 'إخراج مخزون';
      case TransactionType.adjustment:
        return 'تعديل مخزون';
      case TransactionType.sale:
        return 'بيع';
      case TransactionType.return_:
        return 'إرجاع';
      case TransactionType.damaged:
        return 'تالف';
      case TransactionType.expired:
        return 'منتهي الصلاحية';
    }
  }

  String get icon {
    switch (this) {
      case TransactionType.stockIn:
        return '📥';
      case TransactionType.stockOut:
        return '📤';
      case TransactionType.adjustment:
        return '⚖️';
      case TransactionType.sale:
        return '💰';
      case TransactionType.return_:
        return '↩️';
      case TransactionType.damaged:
        return '💔';
      case TransactionType.expired:
        return '⏰';
    }
  }

  String get color {
    switch (this) {
      case TransactionType.stockIn:
        return '#4CAF50'; // أخضر
      case TransactionType.stockOut:
        return '#FF9800'; // برتقالي
      case TransactionType.adjustment:
        return '#2196F3'; // أزرق
      case TransactionType.sale:
        return '#4CAF50'; // أخضر
      case TransactionType.return_:
        return '#9C27B0'; // بنفسجي
      case TransactionType.damaged:
        return '#F44336'; // أحمر
      case TransactionType.expired:
        return '#795548'; // بني
    }
  }

  /// التحقق من كون العملية تزيد المخزون
  bool get increasesStock {
    switch (this) {
      case TransactionType.stockIn:
      case TransactionType.return_:
        return true;
      case TransactionType.stockOut:
      case TransactionType.sale:
      case TransactionType.damaged:
      case TransactionType.expired:
        return false;
      case TransactionType.adjustment:
        return false; // يعتمد على الكمية
    }
  }

  /// التحقق من كون العملية تقلل المخزون
  bool get decreasesStock {
    switch (this) {
      case TransactionType.stockOut:
      case TransactionType.sale:
      case TransactionType.damaged:
      case TransactionType.expired:
        return true;
      case TransactionType.stockIn:
      case TransactionType.return_:
        return false;
      case TransactionType.adjustment:
        return false; // يعتمد على الكمية
    }
  }
}
