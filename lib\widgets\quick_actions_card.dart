import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/providers.dart';
import '../constants/app_constants.dart';
import '../constants/app_theme.dart';

/// ويدجت بطاقة الإجراءات السريعة
/// Quick Actions Card Widget for common operations
class QuickActionsCard extends StatelessWidget {
  const QuickActionsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              childAspectRatio: 2.5,
              crossAxisSpacing: AppConstants.defaultPadding,
              mainAxisSpacing: AppConstants.defaultPadding,
              children: [
                _buildActionButton(
                  context,
                  'إضافة منتج',
                  Icons.add_box,
                  AppTheme.primaryColor,
                  () => _addProduct(context),
                  'add_product',
                ),
                _buildActionButton(
                  context,
                  'مسح باركود',
                  Icons.qr_code_scanner,
                  AppTheme.secondaryColor,
                  () => _scanBarcode(context),
                  null,
                ),
                _buildActionButton(
                  context,
                  'إضافة فئة',
                  Icons.category,
                  AppTheme.warningColor,
                  () => _addCategory(context),
                  'add_product',
                ),
                _buildActionButton(
                  context,
                  'التقارير',
                  Icons.analytics,
                  AppTheme.infoColor,
                  () => _viewReports(context),
                  'view_reports',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
    String? permission,
  ) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // التحقق من الصلاحيات إذا كانت مطلوبة
        if (permission != null && !authProvider.hasPermission(permission)) {
          return const SizedBox.shrink();
        }

        return Material(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            child: Container(
              padding: const EdgeInsets.all(AppConstants.smallPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Flexible(
                    child: Text(
                      title,
                      style: TextStyle(
                        color: color,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _addProduct(BuildContext context) {
    // التنقل إلى شاشة إضافة منتج
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة شاشة إضافة المنتج قريباً'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  void _scanBarcode(BuildContext context) {
    // فتح ماسح الباركود
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ماسح الباركود قريباً'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  void _addCategory(BuildContext context) {
    // التنقل إلى شاشة إضافة فئة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة شاشة إضافة الفئة قريباً'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }

  void _viewReports(BuildContext context) {
    // التنقل إلى شاشة التقارير
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('انتقل إلى تبويب التقارير'),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }
}
