import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/providers.dart';
import '../../constants/app_theme.dart';
import '../dashboard/dashboard_screen.dart';
import '../products/products_screen.dart';
import '../categories/categories_screen.dart';
import '../reports/reports_screen.dart';
import '../profile/profile_screen.dart';

/// الشاشة الرئيسية مع التنقل السفلي
/// Home Screen with bottom navigation
class HomeScreen extends StatefulWidget {
  static const String routeName = '/home';

  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    
    // تحميل البيانات الأساسية
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
    final productProvider = Provider.of<ProductProvider>(context, listen: false);

    // تحميل الفئات والمنتجات
    await Future.wait([
      categoryProvider.loadCategories(),
      productProvider.loadProducts(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Scaffold(
          body: PageView(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            children: _buildPages(authProvider),
          ),
          bottomNavigationBar: _buildBottomNavigationBar(authProvider),
        );
      },
    );
  }

  List<Widget> _buildPages(AuthProvider authProvider) {
    List<Widget> pages = [
      const DashboardScreen(),
      const ProductsScreen(),
      const CategoriesScreen(),
    ];

    // إضافة شاشة التقارير إذا كان المستخدم لديه صلاحية
    if (authProvider.hasPermission('view_reports')) {
      pages.add(const ReportsScreen());
    }

    // إضافة شاشة الملف الشخصي
    pages.add(const ProfileScreen());

    return pages;
  }

  Widget _buildBottomNavigationBar(AuthProvider authProvider) {
    List<BottomNavigationBarItem> items = [
      const BottomNavigationBarItem(
        icon: Icon(Icons.dashboard),
        label: 'الرئيسية',
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.inventory_2),
        label: 'المنتجات',
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.category),
        label: 'الفئات',
      ),
    ];

    // إضافة تبويب التقارير إذا كان المستخدم لديه صلاحية
    if (authProvider.hasPermission('view_reports')) {
      items.add(
        const BottomNavigationBarItem(
          icon: Icon(Icons.analytics),
          label: 'التقارير',
        ),
      );
    }

    // إضافة تبويب الملف الشخصي
    items.add(
      const BottomNavigationBarItem(
        icon: Icon(Icons.person),
        label: 'الملف الشخصي',
      ),
    );

    return BottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: _onTabTapped,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: AppTheme.primaryColor,
      unselectedItemColor: AppTheme.textSecondary,
      backgroundColor: Colors.white,
      elevation: 8,
      items: items,
    );
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }
}
