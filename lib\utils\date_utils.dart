import 'package:intl/intl.dart';
import '../constants/app_constants.dart';

/// أدوات التاريخ والوقت
/// Date and Time Utilities
class AppDateUtils {
  /// تنسيق التاريخ
  static String formatDate(DateTime date) {
    return DateFormat(AppConstants.dateFormat).format(date);
  }

  /// تنسيق الوقت
  static String formatTime(DateTime time) {
    return DateFormat(AppConstants.timeFormat).format(time);
  }

  /// تنسيق التاريخ والوقت
  static String formatDateTime(DateTime dateTime) {
    return DateFormat(AppConstants.dateTimeFormat).format(dateTime);
  }

  /// تنسيق التاريخ والوقت الكامل
  static String formatFullDateTime(DateTime dateTime) {
    return DateFormat(AppConstants.fullDateTimeFormat, 'ar').format(dateTime);
  }

  /// تنسيق التاريخ للتصدير
  static String formatExportDate(DateTime date) {
    return DateFormat(AppConstants.exportDateFormat).format(date);
  }

  /// تنسيق الوقت للتصدير
  static String formatExportTime(DateTime time) {
    return DateFormat(AppConstants.exportTimeFormat).format(time);
  }

  /// الحصول على بداية اليوم
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// الحصول على نهاية اليوم
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// الحصول على بداية الأسبوع
  static DateTime startOfWeek(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return startOfDay(date.subtract(Duration(days: daysFromMonday)));
  }

  /// الحصول على نهاية الأسبوع
  static DateTime endOfWeek(DateTime date) {
    final daysToSunday = 7 - date.weekday;
    return endOfDay(date.add(Duration(days: daysToSunday)));
  }

  /// الحصول على بداية الشهر
  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// الحصول على نهاية الشهر
  static DateTime endOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0, 23, 59, 59, 999);
  }

  /// الحصول على بداية السنة
  static DateTime startOfYear(DateTime date) {
    return DateTime(date.year, 1, 1);
  }

  /// الحصول على نهاية السنة
  static DateTime endOfYear(DateTime date) {
    return DateTime(date.year, 12, 31, 23, 59, 59, 999);
  }

  /// حساب الفرق بين تاريخين بالأيام
  static int daysBetween(DateTime from, DateTime to) {
    from = DateTime(from.year, from.month, from.day);
    to = DateTime(to.year, to.month, to.day);
    return (to.difference(from).inHours / 24).round();
  }

  /// التحقق من كون التاريخ اليوم
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && 
           date.month == now.month && 
           date.day == now.day;
  }

  /// التحقق من كون التاريخ أمس
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year && 
           date.month == yesterday.month && 
           date.day == yesterday.day;
  }

  /// التحقق من كون التاريخ في هذا الأسبوع
  static bool isThisWeek(DateTime date) {
    final now = DateTime.now();
    final startWeek = startOfWeek(now);
    final endWeek = endOfWeek(now);
    return date.isAfter(startWeek.subtract(const Duration(seconds: 1))) &&
           date.isBefore(endWeek.add(const Duration(seconds: 1)));
  }

  /// التحقق من كون التاريخ في هذا الشهر
  static bool isThisMonth(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }

  /// التحقق من كون التاريخ في هذه السنة
  static bool isThisYear(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year;
  }

  /// تحويل التاريخ إلى نص نسبي (منذ كم من الوقت)
  static String timeAgo(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return 'منذ $years ${years == 1 ? 'سنة' : 'سنوات'}';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months ${months == 1 ? 'شهر' : 'أشهر'}';
    } else if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  /// الحصول على قائمة بالأشهر
  static List<String> getMonthNames() {
    return [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
  }

  /// الحصول على قائمة بأيام الأسبوع
  static List<String> getWeekDayNames() {
    return [
      'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
    ];
  }

  /// الحصول على اسم الشهر
  static String getMonthName(int month) {
    final months = getMonthNames();
    return months[month - 1];
  }

  /// الحصول على اسم يوم الأسبوع
  static String getWeekDayName(int weekday) {
    final weekdays = getWeekDayNames();
    return weekdays[weekday - 1];
  }
}
