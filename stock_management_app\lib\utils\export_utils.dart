import 'dart:io';
import 'package:excel/excel.dart';
import 'package:csv/csv.dart';
import 'package:path_provider/path_provider.dart';
import '../constants/app_constants.dart';
import 'date_utils.dart';

/// أدوات التصدير
/// Export Utilities for data export functionality
class ExportUtils {
  /// تصدير البيانات إلى Excel
  static Future<String> exportToExcel({
    required List<Map<String, dynamic>> data,
    required String fileName,
    String? sheetName,
  }) async {
    final excel = Excel.createExcel();
    final sheet = excel[sheetName ?? 'البيانات'];
    
    if (data.isEmpty) {
      throw Exception('لا توجد بيانات للتصدير');
    }
    
    // إضافة العناوين
    final headers = data.first.keys.toList();
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = headers[i];
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: '#E3F2FD',
        horizontalAlign: HorizontalAlign.Center,
      );
    }
    
    // إضافة البيانات
    for (int rowIndex = 0; rowIndex < data.length; rowIndex++) {
      final row = data[rowIndex];
      for (int colIndex = 0; colIndex < headers.length; colIndex++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(
          columnIndex: colIndex, 
          rowIndex: rowIndex + 1,
        ));
        cell.value = row[headers[colIndex]]?.toString() ?? '';
      }
    }
    
    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final timestamp = AppDateUtils.formatExportDate(DateTime.now()).replaceAll('/', '-');
    final fullFileName = '${fileName}_$timestamp.xlsx';
    final filePath = '${directory.path}/${AppConstants.exportsPath}$fullFileName';
    
    // إنشاء مجلد التصدير إذا لم يكن موجوداً
    final exportDir = Directory('${directory.path}/${AppConstants.exportsPath}');
    if (!await exportDir.exists()) {
      await exportDir.create(recursive: true);
    }
    
    final file = File(filePath);
    await file.writeAsBytes(excel.encode()!);
    
    return filePath;
  }

  /// تصدير البيانات إلى CSV
  static Future<String> exportToCSV({
    required List<Map<String, dynamic>> data,
    required String fileName,
  }) async {
    if (data.isEmpty) {
      throw Exception('لا توجد بيانات للتصدير');
    }
    
    // تحضير البيانات للـ CSV
    final headers = data.first.keys.toList();
    final rows = <List<String>>[];
    
    // إضافة العناوين
    rows.add(headers);
    
    // إضافة البيانات
    for (final row in data) {
      final csvRow = headers.map((header) => row[header]?.toString() ?? '').toList();
      rows.add(csvRow);
    }
    
    // تحويل إلى CSV
    final csvData = const ListToCsvConverter().convert(rows);
    
    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final timestamp = AppDateUtils.formatExportDate(DateTime.now()).replaceAll('/', '-');
    final fullFileName = '${fileName}_$timestamp.csv';
    final filePath = '${directory.path}/${AppConstants.exportsPath}$fullFileName';
    
    // إنشاء مجلد التصدير إذا لم يكن موجوداً
    final exportDir = Directory('${directory.path}/${AppConstants.exportsPath}');
    if (!await exportDir.exists()) {
      await exportDir.create(recursive: true);
    }
    
    final file = File(filePath);
    await file.writeAsString(csvData, encoding: utf8);
    
    return filePath;
  }

  /// تصدير المنتجات
  static Future<String> exportProducts(List<Map<String, dynamic>> products, String format) async {
    const fileName = '${AppConstants.exportFileName}_products';
    
    switch (format.toLowerCase()) {
      case 'xlsx':
        return await exportToExcel(
          data: products,
          fileName: fileName,
          sheetName: 'المنتجات',
        );
      case 'csv':
        return await exportToCSV(
          data: products,
          fileName: fileName,
        );
      default:
        throw Exception('تنسيق غير مدعوم: $format');
    }
  }

  /// تصدير الفئات
  static Future<String> exportCategories(List<Map<String, dynamic>> categories, String format) async {
    const fileName = '${AppConstants.exportFileName}_categories';
    
    switch (format.toLowerCase()) {
      case 'xlsx':
        return await exportToExcel(
          data: categories,
          fileName: fileName,
          sheetName: 'الفئات',
        );
      case 'csv':
        return await exportToCSV(
          data: categories,
          fileName: fileName,
        );
      default:
        throw Exception('تنسيق غير مدعوم: $format');
    }
  }

  /// تصدير سجل العمليات
  static Future<String> exportTransactionLogs(List<Map<String, dynamic>> logs, String format) async {
    const fileName = '${AppConstants.exportFileName}_transactions';
    
    switch (format.toLowerCase()) {
      case 'xlsx':
        return await exportToExcel(
          data: logs,
          fileName: fileName,
          sheetName: 'سجل العمليات',
        );
      case 'csv':
        return await exportToCSV(
          data: logs,
          fileName: fileName,
        );
      default:
        throw Exception('تنسيق غير مدعوم: $format');
    }
  }

  /// تصدير تقرير شامل
  static Future<String> exportFullReport({
    required List<Map<String, dynamic>> products,
    required List<Map<String, dynamic>> categories,
    required List<Map<String, dynamic>> transactions,
    String format = 'xlsx',
  }) async {
    if (format.toLowerCase() != 'xlsx') {
      throw Exception('التقرير الشامل متاح فقط بتنسيق Excel');
    }
    
    final excel = Excel.createExcel();
    
    // حذف الورقة الافتراضية
    excel.delete('Sheet1');
    
    // إضافة ورقة المنتجات
    if (products.isNotEmpty) {
      final productsSheet = excel['المنتجات'];
      _addDataToSheet(productsSheet, products);
    }
    
    // إضافة ورقة الفئات
    if (categories.isNotEmpty) {
      final categoriesSheet = excel['الفئات'];
      _addDataToSheet(categoriesSheet, categories);
    }
    
    // إضافة ورقة سجل العمليات
    if (transactions.isNotEmpty) {
      final transactionsSheet = excel['سجل العمليات'];
      _addDataToSheet(transactionsSheet, transactions);
    }
    
    // إضافة ورقة الملخص
    final summarySheet = excel['الملخص'];
    _addSummarySheet(summarySheet, products, categories, transactions);
    
    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final timestamp = AppDateUtils.formatExportDate(DateTime.now()).replaceAll('/', '-');
    final fileName = '${AppConstants.exportFileName}_full_report_$timestamp.xlsx';
    final filePath = '${directory.path}/${AppConstants.exportsPath}$fileName';
    
    // إنشاء مجلد التصدير إذا لم يكن موجوداً
    final exportDir = Directory('${directory.path}/${AppConstants.exportsPath}');
    if (!await exportDir.exists()) {
      await exportDir.create(recursive: true);
    }
    
    final file = File(filePath);
    await file.writeAsBytes(excel.encode()!);
    
    return filePath;
  }

  /// إضافة البيانات إلى ورقة Excel
  static void _addDataToSheet(Sheet sheet, List<Map<String, dynamic>> data) {
    if (data.isEmpty) return;
    
    // إضافة العناوين
    final headers = data.first.keys.toList();
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = headers[i];
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: '#E3F2FD',
        horizontalAlign: HorizontalAlign.Center,
      );
    }
    
    // إضافة البيانات
    for (int rowIndex = 0; rowIndex < data.length; rowIndex++) {
      final row = data[rowIndex];
      for (int colIndex = 0; colIndex < headers.length; colIndex++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(
          columnIndex: colIndex, 
          rowIndex: rowIndex + 1,
        ));
        cell.value = row[headers[colIndex]]?.toString() ?? '';
      }
    }
  }

  /// إضافة ورقة الملخص
  static void _addSummarySheet(
    Sheet sheet,
    List<Map<String, dynamic>> products,
    List<Map<String, dynamic>> categories,
    List<Map<String, dynamic>> transactions,
  ) {
    // عنوان التقرير
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0));
    titleCell.value = 'تقرير إدارة المخازن الشامل';
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
      horizontalAlign: HorizontalAlign.Center,
    );
    
    // تاريخ التقرير
    final dateCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 1));
    dateCell.value = 'تاريخ التقرير: ${AppDateUtils.formatFullDateTime(DateTime.now())}';
    
    // الإحصائيات
    int row = 3;
    
    // إحصائيات المنتجات
    final productsHeaderCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row));
    productsHeaderCell.value = 'إحصائيات المنتجات';
    productsHeaderCell.cellStyle = CellStyle(bold: true, backgroundColorHex: '#FFF3E0');
    row++;
    
    final totalProductsCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row));
    totalProductsCell.value = 'إجمالي المنتجات: ${products.length}';
    row++;
    
    // إحصائيات الفئات
    row++;
    final categoriesHeaderCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row));
    categoriesHeaderCell.value = 'إحصائيات الفئات';
    categoriesHeaderCell.cellStyle = CellStyle(bold: true, backgroundColorHex: '#E8F5E8');
    row++;
    
    final totalCategoriesCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row));
    totalCategoriesCell.value = 'إجمالي الفئات: ${categories.length}';
    row++;
    
    // إحصائيات العمليات
    row++;
    final transactionsHeaderCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row));
    transactionsHeaderCell.value = 'إحصائيات العمليات';
    transactionsHeaderCell.cellStyle = CellStyle(bold: true, backgroundColorHex: '#E3F2FD');
    row++;
    
    final totalTransactionsCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row));
    totalTransactionsCell.value = 'إجمالي العمليات: ${transactions.length}';
  }

  /// الحصول على قائمة الملفات المُصدرة
  static Future<List<FileSystemEntity>> getExportedFiles() async {
    final directory = await getApplicationDocumentsDirectory();
    final exportDir = Directory('${directory.path}/${AppConstants.exportsPath}');
    
    if (!await exportDir.exists()) {
      return [];
    }
    
    return exportDir.listSync().whereType<File>().toList();
  }

  /// حذف ملف مُصدر
  static Future<bool> deleteExportedFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// حذف جميع الملفات المُصدرة
  static Future<int> deleteAllExportedFiles() async {
    final files = await getExportedFiles();
    int deletedCount = 0;
    
    for (final file in files) {
      try {
        await file.delete();
        deletedCount++;
      } catch (e) {
        // تجاهل الأخطاء
      }
    }
    
    return deletedCount;
  }

  /// الحصول على حجم مجلد التصدير
  static Future<int> getExportDirectorySize() async {
    final files = await getExportedFiles();
    int totalSize = 0;
    
    for (final file in files) {
      if (file is File) {
        try {
          final stat = await file.stat();
          totalSize += stat.size;
        } catch (e) {
          // تجاهل الأخطاء
        }
      }
    }
    
    return totalSize;
  }

  /// تنسيق حجم الملف
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes بايت';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} جيجابايت';
    }
  }
}
