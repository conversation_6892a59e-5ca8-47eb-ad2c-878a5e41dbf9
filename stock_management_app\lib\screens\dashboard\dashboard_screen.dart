import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/providers.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../widgets/dashboard_card.dart';
import '../../widgets/stock_alert_card.dart';
import '../../widgets/quick_actions_card.dart';

/// شاشة لوحة التحكم
/// Dashboard Screen showing overview and quick stats
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  Map<String, dynamic> _productStats = {};
  Map<String, dynamic> _categoryStats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final productProvider = Provider.of<ProductProvider>(context, listen: false);
      final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);

      final results = await Future.wait([
        productProvider.getProductStatistics(),
        categoryProvider.getCategoryStatistics(),
      ]);

      setState(() {
        _productStats = results[0];
        _categoryStats = results[1];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة التحكم'),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDashboardData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildWelcomeCard(),
                    const SizedBox(height: AppConstants.defaultPadding),
                    _buildStatsGrid(),
                    const SizedBox(height: AppConstants.defaultPadding),
                    _buildStockAlerts(),
                    const SizedBox(height: AppConstants.defaultPadding),
                    _buildQuickActions(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildWelcomeCard() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: AppTheme.primaryColor,
                  child: Text(
                    authProvider.displayName.isNotEmpty
                        ? authProvider.displayName[0].toUpperCase()
                        : 'U',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مرحباً، ${authProvider.displayName}',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      Text(
                        authProvider.displayRole,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'آخر دخول: ${_formatLastLogin(authProvider.currentUser?.lastLogin)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: AppConstants.defaultPadding,
      mainAxisSpacing: AppConstants.defaultPadding,
      children: [
        DashboardCard(
          title: 'إجمالي المنتجات',
          value: '${_productStats['total_products'] ?? 0}',
          icon: Icons.inventory_2,
          color: AppTheme.primaryColor,
        ),
        DashboardCard(
          title: 'إجمالي الفئات',
          value: '${_categoryStats['active'] ?? 0}',
          icon: Icons.category,
          color: AppTheme.secondaryColor,
        ),
        DashboardCard(
          title: 'كمية قليلة',
          value: '${_productStats['low_stock_count'] ?? 0}',
          icon: Icons.warning,
          color: AppTheme.warningColor,
        ),
        DashboardCard(
          title: 'نفد المخزون',
          value: '${_productStats['out_of_stock_count'] ?? 0}',
          icon: Icons.error,
          color: AppTheme.errorColor,
        ),
      ],
    );
  }

  Widget _buildStockAlerts() {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        final lowStockProducts = productProvider.getLowStockProducts();
        final outOfStockProducts = productProvider.getOutOfStockProducts();

        if (lowStockProducts.isEmpty && outOfStockProducts.isEmpty) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تنبيهات المخزون',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            if (outOfStockProducts.isNotEmpty)
              StockAlertCard(
                title: 'منتجات نفد مخزونها',
                products: outOfStockProducts.take(5).toList(),
                alertType: StockAlertType.outOfStock,
              ),
            if (lowStockProducts.isNotEmpty)
              StockAlertCard(
                title: 'منتجات كميتها قليلة',
                products: lowStockProducts.take(5).toList(),
                alertType: StockAlertType.lowStock,
              ),
          ],
        );
      },
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: AppConstants.smallPadding),
        const QuickActionsCard(),
      ],
    );
  }

  String _formatLastLogin(DateTime? lastLogin) {
    if (lastLogin == null) return 'غير محدد';
    
    final now = DateTime.now();
    final difference = now.difference(lastLogin);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
