import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';

/// شاشة إضافة/تعديل الفئة
/// Add/Edit Category Screen
class AddEditCategoryScreen extends StatefulWidget {
  final Category? category;

  const AddEditCategoryScreen({super.key, this.category});

  @override
  State<AddEditCategoryScreen> createState() => _AddEditCategoryScreenState();
}

class _AddEditCategoryScreenState extends State<AddEditCategoryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  String? _selectedColor;
  bool _isLoading = false;

  final List<String> _availableColors = [
    '#2196F3', // أزرق
    '#E91E63', // وردي
    '#4CAF50', // أخضر
    '#FF9800', // برتقالي
    '#9C27B0', // بنفسجي
    '#00BCD4', // سماوي
    '#CDDC39', // أخضر فاتح
    '#607D8B', // رمادي مزرق
    '#F44336', // أحمر
    '#795548', // بني
  ];

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.category != null) {
      _nameController.text = widget.category!.name;
      _descriptionController.text = widget.category!.description ?? '';
      _selectedColor = widget.category!.color ?? _availableColors.first;
    } else {
      _selectedColor = _availableColors.first;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.category != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'تعديل الفئة' : 'إضافة فئة جديدة'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveCategory,
            child: Text(
              isEditing ? 'تحديث' : 'حفظ',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCategoryPreview(),
              const SizedBox(height: AppConstants.largePadding),
              _buildNameField(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildDescriptionField(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildColorSelector(),
              const SizedBox(height: AppConstants.largePadding),
              _buildSaveButton(isEditing),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryPreview() {
    final color = _selectedColor != null
        ? Color(int.parse(_selectedColor!.replaceFirst('#', '0xFF')))
        : AppTheme.primaryColor;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: color.withOpacity(0.3),
                  width: 2,
                ),
              ),
              child: Icon(
                Icons.category,
                color: color,
                size: 30,
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _nameController.text.isNotEmpty ? _nameController.text : 'اسم الفئة',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (_descriptionController.text.isNotEmpty)
                    Text(
                      _descriptionController.text,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: const InputDecoration(
        labelText: 'اسم الفئة *',
        hintText: 'أدخل اسم الفئة',
        prefixIcon: Icon(Icons.category),
      ),
      textInputAction: TextInputAction.next,
      onChanged: (_) => setState(() {}),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return AppConstants.requiredFieldMessage;
        }
        if (value.trim().length < 2) {
          return 'اسم الفئة يجب أن يكون على الأقل حرفين';
        }
        
        // التحقق من عدم وجود فئة بنفس الاسم
        final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
        if (categoryProvider.isCategoryNameExists(value.trim(), excludeId: widget.category?.id)) {
          return 'اسم الفئة موجود مسبقاً';
        }
        
        return null;
      },
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      decoration: const InputDecoration(
        labelText: 'الوصف',
        hintText: 'أدخل وصف الفئة (اختياري)',
        prefixIcon: Icon(Icons.description),
      ),
      maxLines: 3,
      textInputAction: TextInputAction.done,
      onChanged: (_) => setState(() {}),
    );
  }

  Widget _buildColorSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'لون الفئة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Wrap(
          spacing: AppConstants.smallPadding,
          runSpacing: AppConstants.smallPadding,
          children: _availableColors.map((color) {
            final isSelected = _selectedColor == color;
            final colorValue = Color(int.parse(color.replaceFirst('#', '0xFF')));

            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedColor = color;
                });
              },
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: colorValue,
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: isSelected ? Colors.black : Colors.transparent,
                    width: 3,
                  ),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 24,
                      )
                    : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSaveButton(bool isEditing) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveCategory,
        child: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(isEditing ? 'تحديث الفئة' : 'إضافة الفئة'),
      ),
    );
  }

  Future<void> _saveCategory() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
      
      final category = Category(
        id: widget.category?.id,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isNotEmpty 
            ? _descriptionController.text.trim() 
            : null,
        color: _selectedColor,
        createdAt: widget.category?.createdAt ?? DateTime.now(),
        updatedAt: widget.category != null ? DateTime.now() : null,
      );

      bool success;
      if (widget.category != null) {
        success = await categoryProvider.updateCategory(category);
      } else {
        success = await categoryProvider.addCategory(category);
      }

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.category != null 
                    ? 'تم تحديث الفئة بنجاح'
                    : 'تم إضافة الفئة بنجاح',
              ),
              backgroundColor: AppTheme.successColor,
            ),
          );
          Navigator.of(context).pop();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                categoryProvider.errorMessage ?? 
                (widget.category != null 
                    ? 'فشل في تحديث الفئة'
                    : 'فشل في إضافة الفئة'),
              ),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
