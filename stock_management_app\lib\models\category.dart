/// نموذج الفئة
/// Category Model for product classification
class Category {
  final int? id;
  final String name;
  final String? description;
  final String? color; // لون الفئة للتمييز البصري
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Category({
    this.id,
    required this.name,
    this.description,
    this.color,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  /// تحويل من Map إلى Category object
  factory Category.fromMap(Map<String, dynamic> map) {
    return Category(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      color: map['color'],
      isActive: map['is_active'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at']) 
          : null,
    );
  }

  /// تحويل من Category object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من الفئة
  Category copyWith({
    int? id,
    String? name,
    String? description,
    String? color,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Category{id: $id, name: $name, description: $description}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Category &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name;

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}

/// الفئات الافتراضية للنظام
class DefaultCategories {
  static List<Category> get defaultCategories => [
    Category(
      name: 'إلكترونيات',
      description: 'الأجهزة الإلكترونية والكهربائية',
      color: '#2196F3',
      createdAt: DateTime.now(),
    ),
    Category(
      name: 'ملابس',
      description: 'الملابس والأزياء',
      color: '#E91E63',
      createdAt: DateTime.now(),
    ),
    Category(
      name: 'طعام ومشروبات',
      description: 'المواد الغذائية والمشروبات',
      color: '#4CAF50',
      createdAt: DateTime.now(),
    ),
    Category(
      name: 'أدوات منزلية',
      description: 'الأدوات والمعدات المنزلية',
      color: '#FF9800',
      createdAt: DateTime.now(),
    ),
    Category(
      name: 'كتب وقرطاسية',
      description: 'الكتب والأدوات المكتبية',
      color: '#9C27B0',
      createdAt: DateTime.now(),
    ),
    Category(
      name: 'رياضة وترفيه',
      description: 'المعدات الرياضية وأدوات الترفيه',
      color: '#00BCD4',
      createdAt: DateTime.now(),
    ),
    Category(
      name: 'صحة وجمال',
      description: 'منتجات العناية والجمال',
      color: '#CDDC39',
      createdAt: DateTime.now(),
    ),
    Category(
      name: 'أخرى',
      description: 'منتجات متنوعة',
      color: '#607D8B',
      createdAt: DateTime.now(),
    ),
  ];
}
