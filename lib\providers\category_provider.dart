import 'package:flutter/foundation.dart' hide Category;

import '../models/category.dart';
import '../services/services.dart';

/// مزود الفئات
/// Category Provider for managing category state
class CategoryProvider with ChangeNotifier {
  final CategoryService _categoryService = CategoryService();

  List<Category> _categories = [];
  List<Category> _filteredCategories = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _searchQuery = '';

  // Getters
  List<Category> get categories => _filteredCategories;
  List<Category> get allCategories => _categories;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get searchQuery => _searchQuery;
  int get totalCategories => _categories.length;

  /// تحميل جميع الفئات
  Future<void> loadCategories() async {
    _setLoading(true);
    _clearError();

    try {
      _categories = await _categoryService.getActiveCategories();

      // إذا لم توجد فئات، إنشاء فئات افتراضية
      if (_categories.isEmpty) {
        await _categoryService.createDefaultCategories();
        _categories = await _categoryService.getActiveCategories();
      }

      _applyFilters();
      _setLoading(false);
    } catch (e) {
      _setError('حدث خطأ أثناء تحميل الفئات: ${e.toString()}');
      _setLoading(false);
    }
  }

  /// إضافة فئة جديدة
  Future<bool> addCategory(Category category) async {
    _setLoading(true);
    _clearError();

    try {
      final categoryId = await _categoryService.createCategory(category);
      final newCategory = category.copyWith(id: categoryId);
      _categories.insert(0, newCategory);
      _applyFilters();
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء إضافة الفئة: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// تحديث فئة
  Future<bool> updateCategory(Category category) async {
    _setLoading(true);
    _clearError();

    try {
      await _categoryService.updateCategory(category);
      final index = _categories.indexWhere((c) => c.id == category.id);
      if (index != -1) {
        _categories[index] = category;
        _applyFilters();
      }
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء تحديث الفئة: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// حذف فئة
  Future<bool> deleteCategory(int categoryId) async {
    _setLoading(true);
    _clearError();

    try {
      await _categoryService.deleteCategory(categoryId);
      _categories.removeWhere((c) => c.id == categoryId);
      _applyFilters();
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء حذف الفئة: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// البحث في الفئات
  void searchCategories(String query) {
    _searchQuery = query;
    _applyFilters();
  }

  /// مسح البحث
  void clearSearch() {
    _searchQuery = '';
    _applyFilters();
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    _filteredCategories = _categories.where((category) {
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final matchesName = category.name.toLowerCase().contains(query);
        final matchesDescription =
            category.description?.toLowerCase().contains(query) ?? false;

        return matchesName || matchesDescription;
      }
      return true;
    }).toList();

    notifyListeners();
  }

  /// الحصول على فئة بواسطة المعرف
  Category? getCategoryById(int id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على فئة بواسطة الاسم
  Category? getCategoryByName(String name) {
    try {
      return _categories.firstWhere(
        (category) => category.name.toLowerCase() == name.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  /// الحصول على الفئات مع عدد المنتجات
  Future<List<Map<String, dynamic>>> getCategoriesWithProductCount() async {
    try {
      return await _categoryService.getCategoriesWithProductCount();
    } catch (e) {
      _setError('حدث خطأ أثناء تحميل الفئات: ${e.toString()}');
      return [];
    }
  }

  /// الحصول على أكثر الفئات استخداماً
  Future<List<Map<String, dynamic>>> getMostUsedCategories({
    int limit = 5,
  }) async {
    try {
      return await _categoryService.getMostUsedCategories(limit: limit);
    } catch (e) {
      _setError('حدث خطأ أثناء تحميل الفئات: ${e.toString()}');
      return [];
    }
  }

  /// الحصول على الفئات الفارغة
  Future<List<Category>> getEmptyCategories() async {
    try {
      return await _categoryService.getEmptyCategories();
    } catch (e) {
      _setError('حدث خطأ أثناء تحميل الفئات: ${e.toString()}');
      return [];
    }
  }

  /// تبديل حالة الفئة (نشطة/غير نشطة)
  Future<bool> toggleCategoryStatus(int categoryId, bool isActive) async {
    _setLoading(true);
    _clearError();

    try {
      await _categoryService.toggleCategoryStatus(categoryId, isActive);

      final index = _categories.indexWhere((c) => c.id == categoryId);
      if (index != -1) {
        _categories[index] = _categories[index].copyWith(
          isActive: isActive,
          updatedAt: DateTime.now(),
        );
        _applyFilters();
      }

      _setLoading(false);
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء تغيير حالة الفئة: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// إنشاء الفئات الافتراضية
  Future<void> createDefaultCategories() async {
    try {
      await _categoryService.createDefaultCategories();
      await loadCategories();
    } catch (e) {
      _setError('حدث خطأ أثناء إنشاء الفئات الافتراضية: ${e.toString()}');
    }
  }

  /// التحقق من صحة اسم الفئة
  bool validateCategoryName(String name) {
    return _categoryService.validateCategoryName(name);
  }

  /// التحقق من وجود فئة بنفس الاسم
  bool isCategoryNameExists(String name, {int? excludeId}) {
    return _categories.any(
      (category) =>
          category.name.toLowerCase() == name.toLowerCase() &&
          category.id != excludeId,
    );
  }

  /// الحصول على إحصائيات الفئات
  Future<Map<String, dynamic>> getCategoryStatistics() async {
    try {
      return await _categoryService.getCategoryStatistics();
    } catch (e) {
      _setError('حدث خطأ أثناء تحميل الإحصائيات: ${e.toString()}');
      return {};
    }
  }

  /// نسخ فئة
  Future<bool> duplicateCategory(int categoryId, String newName) async {
    _setLoading(true);
    _clearError();

    try {
      final newCategoryId = await _categoryService.duplicateCategory(
        categoryId,
        newName,
      );

      // إضافة الفئة الجديدة إلى القائمة
      final originalCategory = getCategoryById(categoryId);
      if (originalCategory != null) {
        final newCategory = originalCategory.copyWith(
          id: newCategoryId,
          name: newName,
          createdAt: DateTime.now(),
          updatedAt: null,
        );
        _categories.insert(0, newCategory);
        _applyFilters();
      }

      _setLoading(false);
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء نسخ الفئة: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// دمج فئتين
  Future<bool> mergeCategories(
    int sourceCategoryId,
    int targetCategoryId,
  ) async {
    _setLoading(true);
    _clearError();

    try {
      await _categoryService.mergeCategories(
        sourceCategoryId,
        targetCategoryId,
      );

      // إزالة الفئة المصدر من القائمة
      _categories.removeWhere((c) => c.id == sourceCategoryId);
      _applyFilters();

      _setLoading(false);
      return true;
    } catch (e) {
      _setError('حدث خطأ أثناء دمج الفئات: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// ترتيب الفئات
  void sortCategories(String sortBy, {bool ascending = true}) {
    _categories.sort((a, b) {
      int comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'created_at':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'updated_at':
          final aUpdated = a.updatedAt ?? a.createdAt;
          final bUpdated = b.updatedAt ?? b.createdAt;
          comparison = aUpdated.compareTo(bUpdated);
          break;
        default:
          comparison = a.name.compareTo(b.name);
      }

      return ascending ? comparison : -comparison;
    });

    _applyFilters();
  }

  /// إعادة تحميل فئة واحدة
  Future<void> refreshCategory(int categoryId) async {
    try {
      final category = await _categoryService.getCategoryById(categoryId);
      if (category != null) {
        final index = _categories.indexWhere((c) => c.id == categoryId);
        if (index != -1) {
          _categories[index] = category;
          _applyFilters();
        }
      }
    } catch (e) {
      _setError('حدث خطأ أثناء تحديث الفئة: ${e.toString()}');
    }
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// إعادة تعيين حالة المزود
  void reset() {
    _categories = [];
    _filteredCategories = [];
    _isLoading = false;
    _errorMessage = null;
    _searchQuery = '';
    notifyListeners();
  }
}
