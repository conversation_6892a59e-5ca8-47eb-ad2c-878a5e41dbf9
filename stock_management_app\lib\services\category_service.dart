import '../database/database_helper.dart';
import '../models/models.dart';
import '../constants/app_constants.dart';

/// خدمة الفئات
/// Category Service for category management operations
class CategoryService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء فئة جديدة
  Future<int> createCategory(Category category) async {
    final db = await _databaseHelper.database;
    
    // التحقق من عدم وجود اسم الفئة مسبقاً
    final existingCategory = await getCategoryByName(category.name);
    if (existingCategory != null) {
      throw Exception('اسم الفئة موجود مسبقاً');
    }
    
    return await db.insert(AppConstants.categoriesTable, category.toMap());
  }

  /// الحصول على فئة بواسطة المعرف
  Future<Category?> getCategoryById(int id) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.categoriesTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Category.fromMap(maps.first);
    }
    
    return null;
  }

  /// الحصول على فئة بواسطة الاسم
  Future<Category?> getCategoryByName(String name) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.categoriesTable,
      where: 'name = ?',
      whereArgs: [name],
    );

    if (maps.isNotEmpty) {
      return Category.fromMap(maps.first);
    }
    
    return null;
  }

  /// الحصول على جميع الفئات
  Future<List<Category>> getAllCategories() async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.categoriesTable,
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Category.fromMap(maps[i]);
    });
  }

  /// الحصول على الفئات النشطة
  Future<List<Category>> getActiveCategories() async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.categoriesTable,
      where: 'is_active = 1',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Category.fromMap(maps[i]);
    });
  }

  /// تحديث بيانات الفئة
  Future<int> updateCategory(Category category) async {
    final db = await _databaseHelper.database;
    
    // التحقق من عدم وجود اسم الفئة مسبقاً (باستثناء الفئة الحالية)
    final existingCategory = await getCategoryByName(category.name);
    if (existingCategory != null && existingCategory.id != category.id) {
      throw Exception('اسم الفئة موجود مسبقاً');
    }
    
    final updatedCategory = category.copyWith(
      updatedAt: DateTime.now(),
    );
    
    return await db.update(
      AppConstants.categoriesTable,
      updatedCategory.toMap(),
      where: 'id = ?',
      whereArgs: [category.id],
    );
  }

  /// تفعيل/إلغاء تفعيل الفئة
  Future<int> toggleCategoryStatus(int categoryId, bool isActive) async {
    final db = await _databaseHelper.database;
    
    return await db.update(
      AppConstants.categoriesTable,
      {
        'is_active': isActive ? 1 : 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [categoryId],
    );
  }

  /// حذف الفئة
  Future<int> deleteCategory(int categoryId) async {
    final db = await _databaseHelper.database;
    
    // التحقق من عدم وجود منتجات مرتبطة بالفئة
    final productCount = await _getProductCountByCategory(categoryId);
    if (productCount > 0) {
      throw Exception('لا يمكن حذف الفئة لوجود منتجات مرتبطة بها');
    }
    
    return await db.delete(
      AppConstants.categoriesTable,
      where: 'id = ?',
      whereArgs: [categoryId],
    );
  }

  /// البحث في الفئات
  Future<List<Category>> searchCategories(String query) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.categoriesTable,
      where: 'name LIKE ? OR description LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Category.fromMap(maps[i]);
    });
  }

  /// الحصول على الفئات مع عدد المنتجات
  Future<List<Map<String, dynamic>>> getCategoriesWithProductCount() async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT c.*, COUNT(p.id) as product_count
      FROM ${AppConstants.categoriesTable} c
      LEFT JOIN ${AppConstants.productsTable} p ON c.id = p.category_id AND p.is_active = 1
      WHERE c.is_active = 1
      GROUP BY c.id
      ORDER BY c.name ASC
    ''');

    return maps.map((map) {
      final category = Category.fromMap(map);
      return {
        'category': category,
        'product_count': map['product_count'] ?? 0,
      };
    }).toList();
  }

  /// الحصول على أكثر الفئات استخداماً
  Future<List<Map<String, dynamic>>> getMostUsedCategories({int limit = 5}) async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT c.*, COUNT(p.id) as product_count
      FROM ${AppConstants.categoriesTable} c
      LEFT JOIN ${AppConstants.productsTable} p ON c.id = p.category_id AND p.is_active = 1
      WHERE c.is_active = 1
      GROUP BY c.id
      HAVING product_count > 0
      ORDER BY product_count DESC
      LIMIT ?
    ''', [limit]);

    return maps.map((map) {
      final category = Category.fromMap(map);
      return {
        'category': category,
        'product_count': map['product_count'] ?? 0,
      };
    }).toList();
  }

  /// الحصول على الفئات الفارغة (بدون منتجات)
  Future<List<Category>> getEmptyCategories() async {
    final db = await _databaseHelper.database;
    
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT c.*
      FROM ${AppConstants.categoriesTable} c
      LEFT JOIN ${AppConstants.productsTable} p ON c.id = p.category_id AND p.is_active = 1
      WHERE c.is_active = 1
      GROUP BY c.id
      HAVING COUNT(p.id) = 0
      ORDER BY c.name ASC
    ''');

    return List.generate(maps.length, (i) {
      return Category.fromMap(maps[i]);
    });
  }

  /// إنشاء الفئات الافتراضية
  Future<void> createDefaultCategories() async {
    for (Category category in DefaultCategories.defaultCategories) {
      try {
        await createCategory(category);
      } catch (e) {
        // تجاهل الخطأ إذا كانت الفئة موجودة مسبقاً
        continue;
      }
    }
  }

  /// الحصول على عدد المنتجات في الفئة
  Future<int> _getProductCountByCategory(int categoryId) async {
    final db = await _databaseHelper.database;
    
    final result = await db.rawQuery('''
      SELECT COUNT(*) as count 
      FROM ${AppConstants.productsTable} 
      WHERE category_id = ? AND is_active = 1
    ''', [categoryId]);
    
    return result.first['count'] as int;
  }

  /// التحقق من صحة اسم الفئة
  bool validateCategoryName(String name) {
    // اسم الفئة يجب أن يكون على الأقل حرفين
    return name.trim().length >= 2;
  }

  /// الحصول على إحصائيات الفئات
  Future<Map<String, dynamic>> getCategoryStatistics() async {
    final db = await _databaseHelper.database;
    
    final totalCategories = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.categoriesTable}
    ''');
    
    final activeCategories = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.categoriesTable} WHERE is_active = 1
    ''');
    
    final categoriesWithProducts = await db.rawQuery('''
      SELECT COUNT(DISTINCT c.id) as count
      FROM ${AppConstants.categoriesTable} c
      INNER JOIN ${AppConstants.productsTable} p ON c.id = p.category_id
      WHERE c.is_active = 1 AND p.is_active = 1
    ''');
    
    final emptyCategories = await db.rawQuery('''
      SELECT COUNT(*) as count
      FROM ${AppConstants.categoriesTable} c
      LEFT JOIN ${AppConstants.productsTable} p ON c.id = p.category_id AND p.is_active = 1
      WHERE c.is_active = 1
      GROUP BY c.id
      HAVING COUNT(p.id) = 0
    ''');
    
    return {
      'total': totalCategories.first['count'],
      'active': activeCategories.first['count'],
      'with_products': categoriesWithProducts.first['count'],
      'empty': emptyCategories.length,
    };
  }

  /// نسخ فئة
  Future<int> duplicateCategory(int categoryId, String newName) async {
    final originalCategory = await getCategoryById(categoryId);
    if (originalCategory == null) {
      throw Exception('الفئة غير موجودة');
    }
    
    final newCategory = originalCategory.copyWith(
      id: null,
      name: newName,
      createdAt: DateTime.now(),
      updatedAt: null,
    );
    
    return await createCategory(newCategory);
  }

  /// دمج فئتين
  Future<void> mergeCategories(int sourceCategoryId, int targetCategoryId) async {
    final db = await _databaseHelper.database;
    
    await db.transaction((txn) async {
      // نقل جميع المنتجات من الفئة المصدر إلى الفئة الهدف
      await txn.update(
        AppConstants.productsTable,
        {'category_id': targetCategoryId},
        where: 'category_id = ?',
        whereArgs: [sourceCategoryId],
      );
      
      // حذف الفئة المصدر
      await txn.delete(
        AppConstants.categoriesTable,
        where: 'id = ?',
        whereArgs: [sourceCategoryId],
      );
    });
  }
}
