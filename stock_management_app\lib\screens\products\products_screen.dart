import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/providers.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../models/models.dart';
import '../../widgets/product_card.dart';
import '../../widgets/search_bar_widget.dart';
import '../../widgets/filter_chip_widget.dart';
import 'add_edit_product_screen.dart';
import 'product_details_screen.dart';

/// شاشة المنتجات
/// Products Screen for managing inventory items
class ProductsScreen extends StatefulWidget {
  const ProductsScreen({super.key});

  @override
  State<ProductsScreen> createState() => _ProductsScreenState();
}

class _ProductsScreenState extends State<ProductsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _sortBy = 'name';
  bool _sortAscending = true;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المنتجات'),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: _showSortDialog,
          ),
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              if (authProvider.hasPermission('add_product')) {
                return IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: () => _navigateToAddProduct(),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildProductStats(),
          Expanded(child: _buildProductsList()),
        ],
      ),
      floatingActionButton: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (authProvider.hasPermission('add_product')) {
            return FloatingActionButton(
              onPressed: () => _navigateToAddProduct(),
              child: const Icon(Icons.add),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          SearchBarWidget(
            controller: _searchController,
            hintText: 'البحث في المنتجات...',
            onChanged: (query) {
              Provider.of<ProductProvider>(context, listen: false)
                  .searchProducts(query);
            },
            onClear: () {
              _searchController.clear();
              Provider.of<ProductProvider>(context, listen: false)
                  .searchProducts('');
            },
          ),
          const SizedBox(height: AppConstants.smallPadding),
          _buildFilterChips(),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Consumer2<ProductProvider, CategoryProvider>(
      builder: (context, productProvider, categoryProvider, child) {
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              FilterChipWidget(
                label: 'الكل',
                isSelected: productProvider.selectedCategoryId == null &&
                    productProvider.selectedStockStatus == null,
                onSelected: (selected) {
                  if (selected) {
                    productProvider.clearFilters();
                  }
                },
              ),
              const SizedBox(width: AppConstants.smallPadding),
              ...categoryProvider.categories.map((category) {
                return Padding(
                  padding: const EdgeInsets.only(right: AppConstants.smallPadding),
                  child: FilterChipWidget(
                    label: category.name,
                    isSelected: productProvider.selectedCategoryId == category.id,
                    onSelected: (selected) {
                      productProvider.filterByCategory(
                        selected ? category.id : null,
                      );
                    },
                  ),
                );
              }),
              const SizedBox(width: AppConstants.smallPadding),
              FilterChipWidget(
                label: 'كمية قليلة',
                isSelected: productProvider.selectedStockStatus == StockStatus.lowStock,
                onSelected: (selected) {
                  productProvider.filterByStockStatus(
                    selected ? StockStatus.lowStock : null,
                  );
                },
              ),
              const SizedBox(width: AppConstants.smallPadding),
              FilterChipWidget(
                label: 'نفد المخزون',
                isSelected: productProvider.selectedStockStatus == StockStatus.outOfStock,
                onSelected: (selected) {
                  productProvider.filterByStockStatus(
                    selected ? StockStatus.outOfStock : null,
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProductStats() {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                'المجموع',
                '${productProvider.totalProducts}',
                AppTheme.primaryColor,
              ),
              _buildStatItem(
                'كمية قليلة',
                '${productProvider.lowStockCount}',
                AppTheme.warningColor,
              ),
              _buildStatItem(
                'نفد المخزون',
                '${productProvider.outOfStockCount}',
                AppTheme.errorColor,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildProductsList() {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        if (productProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (productProvider.errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppTheme.errorColor,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  productProvider.errorMessage!,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                ElevatedButton(
                  onPressed: () => productProvider.loadProducts(),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        if (productProvider.products.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inventory_2_outlined,
                  size: 64,
                  color: AppTheme.textSecondary,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'لا توجد منتجات',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  'ابدأ بإضافة منتجات جديدة',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => productProvider.loadProducts(),
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: productProvider.products.length,
            itemBuilder: (context, index) {
              final product = productProvider.products[index];
              return ProductCard(
                product: product,
                onTap: () => _navigateToProductDetails(product),
                onEdit: () => _navigateToEditProduct(product),
                onDelete: () => _showDeleteDialog(product),
              );
            },
          ),
        );
      },
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('ترتيب المنتجات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('الاسم'),
              value: 'name',
              groupValue: _sortBy,
              onChanged: (value) => setState(() => _sortBy = value!),
            ),
            RadioListTile<String>(
              title: const Text('الكمية'),
              value: 'quantity',
              groupValue: _sortBy,
              onChanged: (value) => setState(() => _sortBy = value!),
            ),
            RadioListTile<String>(
              title: const Text('السعر'),
              value: 'price',
              groupValue: _sortBy,
              onChanged: (value) => setState(() => _sortBy = value!),
            ),
            RadioListTile<String>(
              title: const Text('تاريخ الإنشاء'),
              value: 'created_at',
              groupValue: _sortBy,
              onChanged: (value) => setState(() => _sortBy = value!),
            ),
            SwitchListTile(
              title: const Text('ترتيب تصاعدي'),
              value: _sortAscending,
              onChanged: (value) => setState(() => _sortAscending = value),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Provider.of<ProductProvider>(context, listen: false)
                  .sortProducts(_sortBy, ascending: _sortAscending);
              Navigator.pop(context);
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  void _navigateToAddProduct() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddEditProductScreen(),
      ),
    );
  }

  void _navigateToEditProduct(Product product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditProductScreen(product: product),
      ),
    );
  }

  void _navigateToProductDetails(Product product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductDetailsScreen(product: product),
      ),
    );
  }

  void _showDeleteDialog(Product product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المنتج'),
        content: Text('هل أنت متأكد من حذف "${product.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await Provider.of<ProductProvider>(context, listen: false)
                  .deleteProduct(product.id!);
              
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(success ? 'تم حذف المنتج بنجاح' : 'فشل في حذف المنتج'),
                    backgroundColor: success ? AppTheme.successColor : AppTheme.errorColor,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
