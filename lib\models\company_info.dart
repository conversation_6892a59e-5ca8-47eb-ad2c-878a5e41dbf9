/// نموذج معلومات الشركة
/// Company Information Model
class CompanyInfo {
  final String name;
  final String? logo;
  final String? address;
  final String? phone;
  final String? email;
  final String? website;
  final String? taxNumber;
  final String? commercialRegister;
  final String? description;
  final DateTime? establishedDate;

  const CompanyInfo({
    required this.name,
    this.logo,
    this.address,
    this.phone,
    this.email,
    this.website,
    this.taxNumber,
    this.commercialRegister,
    this.description,
    this.establishedDate,
  });

  /// تحويل من Map إلى CompanyInfo
  factory CompanyInfo.fromMap(Map<String, dynamic> map) {
    return CompanyInfo(
      name: map['name'] ?? '',
      logo: map['logo'],
      address: map['address'],
      phone: map['phone'],
      email: map['email'],
      website: map['website'],
      taxNumber: map['tax_number'],
      commercialRegister: map['commercial_register'],
      description: map['description'],
      establishedDate: map['established_date'] != null
          ? DateTime.parse(map['established_date'])
          : null,
    );
  }

  /// تحويل من CompanyInfo إلى Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'logo': logo,
      'address': address,
      'phone': phone,
      'email': email,
      'website': website,
      'tax_number': taxNumber,
      'commercial_register': commercialRegister,
      'description': description,
      'established_date': establishedDate?.toIso8601String(),
    };
  }

  /// إنشاء نسخة معدلة
  CompanyInfo copyWith({
    String? name,
    String? logo,
    String? address,
    String? phone,
    String? email,
    String? website,
    String? taxNumber,
    String? commercialRegister,
    String? description,
    DateTime? establishedDate,
  }) {
    return CompanyInfo(
      name: name ?? this.name,
      logo: logo ?? this.logo,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      website: website ?? this.website,
      taxNumber: taxNumber ?? this.taxNumber,
      commercialRegister: commercialRegister ?? this.commercialRegister,
      description: description ?? this.description,
      establishedDate: establishedDate ?? this.establishedDate,
    );
  }

  @override
  String toString() {
    return 'CompanyInfo(name: $name, address: $address, phone: $phone, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CompanyInfo &&
        other.name == name &&
        other.logo == logo &&
        other.address == address &&
        other.phone == phone &&
        other.email == email &&
        other.website == website &&
        other.taxNumber == taxNumber &&
        other.commercialRegister == commercialRegister &&
        other.description == description &&
        other.establishedDate == establishedDate;
  }

  @override
  int get hashCode {
    return Object.hash(
      name,
      logo,
      address,
      phone,
      email,
      website,
      taxNumber,
      commercialRegister,
      description,
      establishedDate,
    );
  }
}
