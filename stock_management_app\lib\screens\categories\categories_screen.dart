import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/providers.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../models/models.dart';
import '../../widgets/category_card.dart';
import '../../widgets/search_bar_widget.dart';
import 'add_edit_category_screen.dart';

/// شاشة الفئات
/// Categories Screen for managing product categories
class CategoriesScreen extends StatefulWidget {
  const CategoriesScreen({super.key});

  @override
  State<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends State<CategoriesScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الفئات'),
        automaticallyImplyLeading: false,
        actions: [
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              if (authProvider.hasPermission('add_product')) {
                return IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: () => _navigateToAddCategory(),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildCategoryStats(),
          Expanded(child: _buildCategoriesList()),
        ],
      ),
      floatingActionButton: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (authProvider.hasPermission('add_product')) {
            return FloatingActionButton(
              onPressed: () => _navigateToAddCategory(),
              child: const Icon(Icons.add),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: SearchBarWidget(
        controller: _searchController,
        hintText: 'البحث في الفئات...',
        onChanged: (query) {
          Provider.of<CategoryProvider>(context, listen: false)
              .searchCategories(query);
        },
        onClear: () {
          _searchController.clear();
          Provider.of<CategoryProvider>(context, listen: false)
              .searchCategories('');
        },
      ),
    );
  }

  Widget _buildCategoryStats() {
    return Consumer<CategoryProvider>(
      builder: (context, categoryProvider, child) {
        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildStatItem(
                'إجمالي الفئات',
                '${categoryProvider.totalCategories}',
                AppTheme.primaryColor,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildCategoriesList() {
    return Consumer<CategoryProvider>(
      builder: (context, categoryProvider, child) {
        if (categoryProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (categoryProvider.errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppTheme.errorColor,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  categoryProvider.errorMessage!,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                ElevatedButton(
                  onPressed: () => categoryProvider.loadCategories(),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        if (categoryProvider.categories.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.category_outlined,
                  size: 64,
                  color: AppTheme.textSecondary,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'لا توجد فئات',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  'ابدأ بإضافة فئات جديدة',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                ElevatedButton(
                  onPressed: () => categoryProvider.createDefaultCategories(),
                  child: const Text('إنشاء فئات افتراضية'),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => categoryProvider.loadCategories(),
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: categoryProvider.categories.length,
            itemBuilder: (context, index) {
              final category = categoryProvider.categories[index];
              return CategoryCard(
                category: category,
                onTap: () => _showCategoryDetails(category),
                onEdit: () => _navigateToEditCategory(category),
                onDelete: () => _showDeleteDialog(category),
              );
            },
          ),
        );
      },
    );
  }

  void _navigateToAddCategory() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddEditCategoryScreen(),
      ),
    );
  }

  void _navigateToEditCategory(Category category) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditCategoryScreen(category: category),
      ),
    );
  }

  void _showCategoryDetails(Category category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(category.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (category.description != null && category.description!.isNotEmpty)
              Text('الوصف: ${category.description}'),
            const SizedBox(height: 8),
            Text('تاريخ الإنشاء: ${_formatDate(category.createdAt)}'),
            if (category.updatedAt != null)
              Text('آخر تحديث: ${_formatDate(category.updatedAt!)}'),
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: category.color != null 
                        ? Color(int.parse(category.color!.replaceFirst('#', '0xFF')))
                        : AppTheme.primaryColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                const SizedBox(width: 8),
                Text('لون الفئة'),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              if (authProvider.hasPermission('edit_product')) {
                return ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _navigateToEditCategory(category);
                  },
                  child: const Text('تعديل'),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(Category category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الفئة'),
        content: Text('هل أنت متأكد من حذف فئة "${category.name}"؟\n\nملاحظة: لا يمكن حذف الفئة إذا كانت تحتوي على منتجات.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await Provider.of<CategoryProvider>(context, listen: false)
                  .deleteCategory(category.id!);
              
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(success ? 'تم حذف الفئة بنجاح' : 'فشل في حذف الفئة'),
                    backgroundColor: success ? AppTheme.successColor : AppTheme.errorColor,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
