import 'package:flutter/foundation.dart';

import '../models/models.dart';
import '../services/services.dart';

/// مزود المشتريات
/// Purchase Provider for managing purchase state
class PurchaseProvider with ChangeNotifier {
  final PurchaseService _purchaseService = PurchaseService();

  List<Purchase> _purchases = [];
  List<Purchase> _filteredPurchases = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _searchQuery = '';
  PurchaseStatus? _selectedStatus;
  DateTime? _startDate;
  DateTime? _endDate;

  // Getters
  List<Purchase> get purchases => _filteredPurchases;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get searchQuery => _searchQuery;
  PurchaseStatus? get selectedStatus => _selectedStatus;
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;

  /// تحميل جميع المشتريات
  Future<void> loadPurchases() async {
    _setLoading(true);
    _clearError();

    try {
      _purchases = await _purchaseService.getAllPurchases();
      _applyFilters();
    } catch (e) {
      _setError('فشل في تحميل المشتريات: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// إنشاء مشترى جديد
  Future<bool> createPurchase(Purchase purchase, List<PurchaseItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      await _purchaseService.createPurchase(purchase, items);
      await loadPurchases(); // إعادة تحميل القائمة
      return true;
    } catch (e) {
      _setError('فشل في إنشاء المشترى: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// الحصول على مشترى بالمعرف
  Future<Purchase?> getPurchaseById(int id) async {
    try {
      return await _purchaseService.getPurchaseById(id);
    } catch (e) {
      _setError('فشل في الحصول على المشترى: ${e.toString()}');
      return null;
    }
  }

  /// الحصول على عناصر المشترى
  Future<List<PurchaseItem>> getPurchaseItems(int purchaseId) async {
    try {
      return await _purchaseService.getPurchaseItems(purchaseId);
    } catch (e) {
      _setError('فشل في الحصول على عناصر المشترى: ${e.toString()}');
      return [];
    }
  }

  /// تحديث حالة المشترى
  Future<bool> updatePurchaseStatus(int purchaseId, PurchaseStatus status) async {
    _setLoading(true);
    _clearError();

    try {
      await _purchaseService.updatePurchaseStatus(purchaseId, status);
      await loadPurchases(); // إعادة تحميل القائمة
      return true;
    } catch (e) {
      _setError('فشل في تحديث حالة المشترى: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث دفعة المشترى
  Future<bool> updatePurchasePayment(int purchaseId, double paidAmount) async {
    _setLoading(true);
    _clearError();

    try {
      await _purchaseService.updatePurchasePayment(purchaseId, paidAmount);
      await loadPurchases(); // إعادة تحميل القائمة
      return true;
    } catch (e) {
      _setError('فشل في تحديث دفعة المشترى: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// حذف مشترى
  Future<bool> deletePurchase(int purchaseId) async {
    _setLoading(true);
    _clearError();

    try {
      await _purchaseService.deletePurchase(purchaseId);
      await loadPurchases(); // إعادة تحميل القائمة
      return true;
    } catch (e) {
      _setError('فشل في حذف المشترى: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// البحث في المشتريات
  void searchPurchases(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }

  /// تصفية المشتريات حسب الحالة
  void filterByStatus(PurchaseStatus? status) {
    _selectedStatus = status;
    _applyFilters();
    notifyListeners();
  }

  /// تصفية المشتريات حسب التاريخ
  void filterByDateRange(DateTime? startDate, DateTime? endDate) {
    _startDate = startDate;
    _endDate = endDate;
    _applyFilters();
    notifyListeners();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery = '';
    _selectedStatus = null;
    _startDate = null;
    _endDate = null;
    _applyFilters();
    notifyListeners();
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    _filteredPurchases = _purchases.where((purchase) {
      // تصفية البحث
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!purchase.invoiceNumber.toLowerCase().contains(query) &&
            !purchase.supplierName.toLowerCase().contains(query) &&
            !(purchase.notes?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // تصفية الحالة
      if (_selectedStatus != null && purchase.status != _selectedStatus) {
        return false;
      }

      // تصفية التاريخ
      if (_startDate != null && purchase.purchaseDate.isBefore(_startDate!)) {
        return false;
      }
      if (_endDate != null && purchase.purchaseDate.isAfter(_endDate!)) {
        return false;
      }

      return true;
    }).toList();
  }

  /// الحصول على إحصائيات المشتريات
  Future<Map<String, dynamic>> getPurchaseStats() async {
    try {
      return await _purchaseService.getPurchaseStats();
    } catch (e) {
      _setError('فشل في الحصول على إحصائيات المشتريات: ${e.toString()}');
      return {};
    }
  }

  /// توليد رقم فاتورة جديد
  Future<String> generateInvoiceNumber() async {
    try {
      return await _purchaseService.generateInvoiceNumber();
    } catch (e) {
      _setError('فشل في توليد رقم الفاتورة: ${e.toString()}');
      return '';
    }
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void _clearError() {
    _errorMessage = null;
  }

  /// مسح البيانات
  void clearData() {
    _purchases.clear();
    _filteredPurchases.clear();
    _searchQuery = '';
    _selectedStatus = null;
    _startDate = null;
    _endDate = null;
    _errorMessage = null;
    notifyListeners();
  }
}
