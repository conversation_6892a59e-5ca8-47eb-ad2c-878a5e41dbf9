# خاصية إدارة العملة - Currency Management Feature

## نظرة عامة
تم إضافة خاصية إدارة العملة إلى تطبيق إدارة المخزون، والتي تسمح للمستخدمين بتغيير رمز العملة المستخدم في عرض الأسعار عبر التطبيق.

## الميزات الجديدة

### 1. إعدادات العملة
- **موقع الإعداد**: الإعدادات > إعدادات التطبيق > العملة
- **العملات المدعومة**: 20 عملة مختلفة تشمل العملات العربية والعالمية
- **حفظ الإعدادات**: يتم حفظ اختيار العملة محلياً وتطبيقه على جميع الأسعار

### 2. العملات المدعومة
```
العملات العربية:
- ر.س (الريال السعودي)
- د.إ (الدرهم الإماراتي)
- د.ك (الدينار الكويتي)
- ر.ق (الريال القطري)
- د.ب (الدينار البحريني)
- ر.ع (الريال العماني)
- د.أ (الدينار الأردني)
- ج.م (الجنيه المصري)
- ل.س (الليرة السورية)
- د.ع (الدينار العراقي)
- ل.ل (الليرة اللبنانية)
- د.م (الدرهم المغربي)
- د.ت (الدينار التونسي)
- د.ج (الدينار الجزائري)
- ج.س (الجنيه السوداني)

العملات العالمية:
- $ (الدولار الأمريكي)
- € (اليورو)
- £ (الجنيه الإسترليني)
- ¥ (الين الياباني)
- ₹ (الروبية الهندية)
```

### 3. الأماكن المتأثرة بتغيير العملة
- **بطاقات المنتجات**: عرض أسعار البيع
- **تفاصيل المنتج**: أسعار الشراء والبيع
- **شاشة إضافة/تعديل المنتج**: عرض الأسعار أثناء الإدخال
- **التقارير**: جميع القيم المالية
- **الإحصائيات**: القيم الإجمالية

## التطبيق التقني

### 1. SettingsProvider
```dart
class SettingsProvider with ChangeNotifier {
  String _currencySymbol = AppConstants.currencySymbol;
  
  // تغيير رمز العملة
  Future<void> setCurrencySymbol(String symbol) async {
    if (_currencySymbol != symbol) {
      _currencySymbol = symbol;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.currencyKey, symbol);
      notifyListeners();
    }
  }
  
  // تنسيق المبلغ مع رمز العملة
  String formatCurrency(double amount) {
    return '${amount.toStringAsFixed(AppConstants.decimalPlaces)} $_currencySymbol';
  }
}
```

### 2. استخدام العملة في الواجهات
```dart
// في ProductCard
Consumer<SettingsProvider>(
  builder: (context, settingsProvider, child) {
    return Text(
      'السعر: ${settingsProvider.formatCurrency(product.sellingPrice)}',
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        fontWeight: FontWeight.w600,
        color: AppTheme.primaryColor,
      ),
    );
  },
)
```

### 3. حفظ الإعدادات
- يتم حفظ اختيار العملة في `SharedPreferences`
- يتم تحميل الإعدادات عند بدء التطبيق
- تطبيق فوري للتغييرات على جميع الواجهات

## كيفية الاستخدام

### للمستخدم النهائي:
1. افتح التطبيق
2. اذهب إلى "الإعدادات" من شريط التنقل السفلي
3. اضغط على "العملة" في قسم "إعدادات التطبيق"
4. اختر العملة المطلوبة من القائمة
5. سيتم تطبيق التغيير فوراً على جميع الأسعار

### للمطور:
```dart
// للحصول على رمز العملة الحالي
final settingsProvider = Provider.of<SettingsProvider>(context);
String currentCurrency = settingsProvider.currencySymbol;

// لتنسيق مبلغ مع العملة
String formattedPrice = settingsProvider.formatCurrency(100.50);
// النتيجة: "100.50 ر.س" (حسب العملة المختارة)

// للتحقق من صحة رمز العملة
bool isValid = settingsProvider.isValidCurrency('$');
```

## الملفات المتأثرة

### ملفات جديدة:
- `lib/providers/settings_provider.dart` - مزود إعدادات التطبيق
- `CURRENCY_FEATURE.md` - توثيق الميزة

### ملفات محدثة:
- `lib/constants/app_constants.dart` - إضافة العملات المدعومة
- `lib/providers/providers.dart` - تصدير SettingsProvider
- `lib/main.dart` - إضافة SettingsProvider للتطبيق
- `lib/screens/settings/settings_screen.dart` - إضافة خيار العملة
- `lib/widgets/product_card.dart` - استخدام العملة الديناميكية
- `lib/screens/products/product_details_screen.dart` - استخدام العملة الديناميكية

## المزايا
1. **مرونة**: دعم 20 عملة مختلفة
2. **سهولة الاستخدام**: واجهة بسيطة لتغيير العملة
3. **الاستمرارية**: حفظ الإعدادات بين جلسات التطبيق
4. **التطبيق الفوري**: تحديث جميع الأسعار فوراً
5. **التوافق**: يعمل مع جميع أجزاء التطبيق

## التطوير المستقبلي
- إضافة أسعار صرف ديناميكية
- دعم عملات إضافية
- تنسيق الأرقام حسب المنطقة
- إعدادات متقدمة للعرض (عدد الخانات العشرية)

## الاختبار
تم اختبار الميزة على:
- ✅ تغيير العملة من الإعدادات
- ✅ عرض الأسعار في بطاقات المنتجات
- ✅ عرض الأسعار في تفاصيل المنتج
- ✅ حفظ واستعادة الإعدادات
- ✅ التطبيق الفوري للتغييرات

## الإصدار
- **تاريخ الإضافة**: 2025-01-27
- **الإصدار**: 1.1.0
- **المطور**: فريق التطوير
