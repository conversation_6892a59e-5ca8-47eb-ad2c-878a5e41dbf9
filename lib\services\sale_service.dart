import '../database/database_helper.dart';
import '../models/models.dart';
import '../constants/app_constants.dart';

/// خدمة المبيعات
/// Sale Service for managing sales operations
class SaleService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء مبيعة جديدة
  Future<int> createSale(Sale sale, List<SaleItem> items) async {
    final db = await _databaseHelper.database;

    return await db.transaction((txn) async {
      // التحقق من توفر الكميات
      for (final item in items) {
        final productResult = await txn.query(
          AppConstants.productsTable,
          columns: ['quantity'],
          where: 'id = ?',
          whereArgs: [item.productId],
        );

        if (productResult.isEmpty) {
          throw Exception('المنتج غير موجود: ${item.productName}');
        }

        final currentQuantity = productResult.first['quantity'] as int;
        if (currentQuantity < item.quantity) {
          throw Exception('الكمية المتوفرة غير كافية للمنتج: ${item.productName}');
        }
      }

      // إدراج المبيعة
      final saleId = await txn.insert(
        AppConstants.salesTable,
        sale.toMap(),
      );

      // إدراج عناصر المبيعة وتحديث المخزون
      for (final item in items) {
        final itemWithSaleId = item.copyWith(saleId: saleId);
        await txn.insert(
          AppConstants.saleItemsTable,
          itemWithSaleId.toMap(),
        );

        // تحديث كمية المنتج في المخزون
        await txn.rawUpdate('''
          UPDATE ${AppConstants.productsTable} 
          SET quantity = quantity - ?, updated_at = ?
          WHERE id = ?
        ''', [item.quantity, DateTime.now().toIso8601String(), item.productId]);

        // إضافة سجل العملية
        final transactionLog = TransactionLog(
          productId: item.productId,
          userId: sale.userId,
          type: TransactionType.sale,
          quantity: item.quantity,
          previousQuantity: 0, // سيتم تحديثه لاحقاً
          newQuantity: 0, // سيتم تحديثه لاحقاً
          notes: 'مبيعة رقم: ${sale.invoiceNumber}',
          createdAt: DateTime.now(),
        );

        await txn.insert(
          AppConstants.transactionLogsTable,
          transactionLog.toMap(),
        );
      }

      return saleId;
    });
  }

  /// الحصول على جميع المبيعات
  Future<List<Sale>> getAllSales() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.salesTable,
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Sale.fromMap(maps[i]);
    });
  }

  /// الحصول على مبيعة بالمعرف
  Future<Sale?> getSaleById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.salesTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Sale.fromMap(maps.first);
    }
    return null;
  }

  /// الحصول على عناصر المبيعة
  Future<List<SaleItem>> getSaleItems(int saleId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.saleItemsTable,
      where: 'sale_id = ?',
      whereArgs: [saleId],
      orderBy: 'created_at ASC',
    );

    return List.generate(maps.length, (i) {
      return SaleItem.fromMap(maps[i]);
    });
  }

  /// تحديث حالة المبيعة
  Future<int> updateSaleStatus(int saleId, SaleStatus status) async {
    final db = await _databaseHelper.database;

    return await db.update(
      AppConstants.salesTable,
      {
        'status': status.index,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [saleId],
    );
  }

  /// تحديث دفعة المبيعة
  Future<int> updateSalePayment(int saleId, double paidAmount) async {
    final db = await _databaseHelper.database;

    // الحصول على المبيعة الحالية
    final sale = await getSaleById(saleId);
    if (sale == null) return 0;

    final newPaidAmount = sale.paidAmount + paidAmount;
    final newRemainingAmount = sale.totalAmount - newPaidAmount - sale.discountAmount;

    return await db.update(
      AppConstants.salesTable,
      {
        'paid_amount': newPaidAmount,
        'remaining_amount': newRemainingAmount,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [saleId],
    );
  }

  /// إرجاع مبيعة
  Future<int> returnSale(int saleId, int userId) async {
    final db = await _databaseHelper.database;

    return await db.transaction((txn) async {
      // الحصول على عناصر المبيعة
      final items = await getSaleItems(saleId);

      // إرجاع الكميات إلى المخزون
      for (final item in items) {
        await txn.rawUpdate('''
          UPDATE ${AppConstants.productsTable} 
          SET quantity = quantity + ?, updated_at = ?
          WHERE id = ?
        ''', [item.quantity, DateTime.now().toIso8601String(), item.productId]);

        // إضافة سجل العملية
        final transactionLog = TransactionLog(
          productId: item.productId,
          userId: userId,
          type: TransactionType.return_,
          quantity: item.quantity,
          previousQuantity: 0, // سيتم تحديثه لاحقاً
          newQuantity: 0, // سيتم تحديثه لاحقاً
          notes: 'إرجاع مبيعة رقم: ${(await getSaleById(saleId))?.invoiceNumber}',
          createdAt: DateTime.now(),
        );

        await txn.insert(
          AppConstants.transactionLogsTable,
          transactionLog.toMap(),
        );
      }

      // تحديث حالة المبيعة
      return await txn.update(
        AppConstants.salesTable,
        {
          'status': SaleStatus.returned.index,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [saleId],
      );
    });
  }

  /// حذف مبيعة
  Future<int> deleteSale(int saleId) async {
    final db = await _databaseHelper.database;

    return await db.transaction((txn) async {
      // حذف عناصر المبيعة
      await txn.delete(
        AppConstants.saleItemsTable,
        where: 'sale_id = ?',
        whereArgs: [saleId],
      );

      // حذف المبيعة
      return await txn.delete(
        AppConstants.salesTable,
        where: 'id = ?',
        whereArgs: [saleId],
      );
    });
  }

  /// البحث في المبيعات
  Future<List<Sale>> searchSales(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.salesTable,
      where: '''
        invoice_number LIKE ? OR 
        customer_name LIKE ? OR 
        notes LIKE ?
      ''',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Sale.fromMap(maps[i]);
    });
  }

  /// الحصول على المبيعات حسب التاريخ
  Future<List<Sale>> getSalesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.salesTable,
      where: 'sale_date BETWEEN ? AND ?',
      whereArgs: [
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
      orderBy: 'sale_date DESC',
    );

    return List.generate(maps.length, (i) {
      return Sale.fromMap(maps[i]);
    });
  }

  /// الحصول على المبيعات حسب الحالة
  Future<List<Sale>> getSalesByStatus(SaleStatus status) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.salesTable,
      where: 'status = ?',
      whereArgs: [status.index],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Sale.fromMap(maps[i]);
    });
  }

  /// الحصول على إحصائيات المبيعات
  Future<Map<String, dynamic>> getSalesStats() async {
    final db = await _databaseHelper.database;

    // إجمالي المبيعات
    final totalSales = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.salesTable}
    ''');

    // إجمالي قيمة المبيعات
    final totalValue = await db.rawQuery('''
      SELECT SUM(total_amount) as total FROM ${AppConstants.salesTable}
    ''');

    // إجمالي المدفوع
    final totalPaid = await db.rawQuery('''
      SELECT SUM(paid_amount) as total FROM ${AppConstants.salesTable}
    ''');

    // إجمالي المتبقي
    final totalRemaining = await db.rawQuery('''
      SELECT SUM(remaining_amount) as total FROM ${AppConstants.salesTable}
    ''');

    // إجمالي الخصومات
    final totalDiscounts = await db.rawQuery('''
      SELECT SUM(discount_amount) as total FROM ${AppConstants.salesTable}
    ''');

    return {
      'total_sales': totalSales.first['count'],
      'total_value': totalValue.first['total'] ?? 0.0,
      'total_paid': totalPaid.first['total'] ?? 0.0,
      'total_remaining': totalRemaining.first['total'] ?? 0.0,
      'total_discounts': totalDiscounts.first['total'] ?? 0.0,
    };
  }

  /// توليد رقم فاتورة جديد
  Future<String> generateInvoiceNumber() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('''
      SELECT COUNT(*) as count FROM ${AppConstants.salesTable}
    ''');

    final count = result.first['count'] as int;
    final now = DateTime.now();
    return 'SAL-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${(count + 1).toString().padLeft(4, '0')}';
  }
}
