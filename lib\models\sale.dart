/// نموذج المبيعات
/// Sale Model for managing sales transactions
class Sale {
  final int? id;
  final String invoiceNumber;
  final String? customerName;
  final String? customerPhone;
  final String? customerAddress;
  final double totalAmount;
  final double paidAmount;
  final double remainingAmount;
  final double discountAmount;
  final SaleStatus status;
  final PaymentMethod paymentMethod;
  final DateTime saleDate;
  final String? notes;
  final int userId;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Sale({
    this.id,
    required this.invoiceNumber,
    this.customerName,
    this.customerPhone,
    this.customerAddress,
    required this.totalAmount,
    this.paidAmount = 0.0,
    double? remainingAmount,
    this.discountAmount = 0.0,
    this.status = SaleStatus.pending,
    this.paymentMethod = PaymentMethod.cash,
    required this.saleDate,
    this.notes,
    required this.userId,
    required this.createdAt,
    this.updatedAt,
  }) : remainingAmount = remainingAmount ?? (totalAmount - paidAmount - discountAmount);

  /// تحويل من Map إلى Sale object
  factory Sale.fromMap(Map<String, dynamic> map) {
    return Sale(
      id: map['id'],
      invoiceNumber: map['invoice_number'],
      customerName: map['customer_name'],
      customerPhone: map['customer_phone'],
      customerAddress: map['customer_address'],
      totalAmount: map['total_amount'].toDouble(),
      paidAmount: map['paid_amount'].toDouble(),
      remainingAmount: map['remaining_amount'].toDouble(),
      discountAmount: map['discount_amount'].toDouble(),
      status: SaleStatus.values[map['status']],
      paymentMethod: PaymentMethod.values[map['payment_method']],
      saleDate: DateTime.parse(map['sale_date']),
      notes: map['notes'],
      userId: map['user_id'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at']) 
          : null,
    );
  }

  /// تحويل من Sale object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'customer_address': customerAddress,
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'remaining_amount': remainingAmount,
      'discount_amount': discountAmount,
      'status': status.index,
      'payment_method': paymentMethod.index,
      'sale_date': saleDate.toIso8601String(),
      'notes': notes,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من المبيعة
  Sale copyWith({
    int? id,
    String? invoiceNumber,
    String? customerName,
    String? customerPhone,
    String? customerAddress,
    double? totalAmount,
    double? paidAmount,
    double? remainingAmount,
    double? discountAmount,
    SaleStatus? status,
    PaymentMethod? paymentMethod,
    DateTime? saleDate,
    String? notes,
    int? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Sale(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      customerAddress: customerAddress ?? this.customerAddress,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      saleDate: saleDate ?? this.saleDate,
      notes: notes ?? this.notes,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من المساواة
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Sale &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  /// التحقق من كون المبيعة مكتملة الدفع
  bool get isFullyPaid => remainingAmount <= 0;

  /// التحقق من كون المبيعة مدفوعة جزئياً
  bool get isPartiallyPaid => paidAmount > 0 && remainingAmount > 0;

  /// التحقق من كون المبيعة غير مدفوعة
  bool get isUnpaid => paidAmount <= 0;

  /// حساب صافي المبلغ بعد الخصم
  double get netAmount => totalAmount - discountAmount;
}

/// حالات المبيعات
enum SaleStatus {
  pending,    // في الانتظار
  completed,  // مكتملة
  cancelled,  // ملغية
  returned,   // مرتجعة
}

extension SaleStatusExtension on SaleStatus {
  String get displayName {
    switch (this) {
      case SaleStatus.pending:
        return 'في الانتظار';
      case SaleStatus.completed:
        return 'مكتملة';
      case SaleStatus.cancelled:
        return 'ملغية';
      case SaleStatus.returned:
        return 'مرتجعة';
    }
  }

  String get color {
    switch (this) {
      case SaleStatus.pending:
        return '#FF9800'; // برتقالي
      case SaleStatus.completed:
        return '#4CAF50'; // أخضر
      case SaleStatus.cancelled:
        return '#F44336'; // أحمر
      case SaleStatus.returned:
        return '#9C27B0'; // بنفسجي
    }
  }
}

/// طرق الدفع
enum PaymentMethod {
  cash,        // نقدي
  card,        // بطاقة
  transfer,    // تحويل
  check,       // شيك
  credit,      // آجل
}

extension PaymentMethodExtension on PaymentMethod {
  String get displayName {
    switch (this) {
      case PaymentMethod.cash:
        return 'نقدي';
      case PaymentMethod.card:
        return 'بطاقة';
      case PaymentMethod.transfer:
        return 'تحويل';
      case PaymentMethod.check:
        return 'شيك';
      case PaymentMethod.credit:
        return 'آجل';
    }
  }

  String get icon {
    switch (this) {
      case PaymentMethod.cash:
        return '💵';
      case PaymentMethod.card:
        return '💳';
      case PaymentMethod.transfer:
        return '🏦';
      case PaymentMethod.check:
        return '📝';
      case PaymentMethod.credit:
        return '📅';
    }
  }
}

/// عنصر المبيعات
/// Sale Item Model for individual items in a sale
class SaleItem {
  final int? id;
  final int saleId;
  final int productId;
  final String productName;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final double discountAmount;
  final DateTime createdAt;

  SaleItem({
    this.id,
    required this.saleId,
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    double? totalPrice,
    this.discountAmount = 0.0,
    required this.createdAt,
  }) : totalPrice = totalPrice ?? ((quantity * unitPrice) - discountAmount);

  /// تحويل من Map إلى SaleItem object
  factory SaleItem.fromMap(Map<String, dynamic> map) {
    return SaleItem(
      id: map['id'],
      saleId: map['sale_id'],
      productId: map['product_id'],
      productName: map['product_name'],
      quantity: map['quantity'],
      unitPrice: map['unit_price'].toDouble(),
      totalPrice: map['total_price'].toDouble(),
      discountAmount: map['discount_amount'].toDouble(),
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  /// تحويل من SaleItem object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'sale_id': saleId,
      'product_id': productId,
      'product_name': productName,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'discount_amount': discountAmount,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من عنصر المبيعة
  SaleItem copyWith({
    int? id,
    int? saleId,
    int? productId,
    String? productName,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
    double? discountAmount,
    DateTime? createdAt,
  }) {
    return SaleItem(
      id: id ?? this.id,
      saleId: saleId ?? this.saleId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      discountAmount: discountAmount ?? this.discountAmount,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// التحقق من المساواة
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SaleItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  /// حساب صافي السعر بعد الخصم
  double get netPrice => (unitPrice * quantity) - discountAmount;
}
