import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../providers/providers.dart';

/// شاشة التقارير
/// Reports Screen for viewing analytics and statistics
class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  Map<String, dynamic> _productStats = {};
  Map<String, dynamic> _categoryStats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadReportsData();
  }

  Future<void> _loadReportsData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final productProvider = Provider.of<ProductProvider>(
        context,
        listen: false,
      );
      final categoryProvider = Provider.of<CategoryProvider>(
        context,
        listen: false,
      );

      final results = await Future.wait([
        productProvider.getProductStatistics(),
        categoryProvider.getCategoryStatistics(),
      ]);

      setState(() {
        _productStats = results[0];
        _categoryStats = results[1];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل التقارير: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.blue.shade50, Colors.white],
          ),
        ),
        child: _isLoading
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('جاري تحميل التقارير...'),
                  ],
                ),
              )
            : RefreshIndicator(
                onRefresh: _loadReportsData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildModernHeader(),
                      const SizedBox(height: 24),
                      _buildModernOverviewSection(),
                      const SizedBox(height: 24),
                      _buildModernInventorySection(),
                      const SizedBox(height: 24),
                      _buildModernFinancialSection(),
                      const SizedBox(height: 24),
                      _buildModernCategorySection(),
                      const SizedBox(height: 100), // مساحة للتنقل السفلي
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildModernHeader() {
    return Container(
      padding: const EdgeInsets.all(20.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade600, Colors.blue.shade800],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(15),
            ),
            child: const Icon(Icons.analytics, color: Colors.white, size: 30),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'التقارير والإحصائيات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                const Text(
                  'تقرير شامل عن حالة المخزون',
                  style: TextStyle(fontSize: 14, color: Colors.white70),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'آخر تحديث: ${DateTime.now().toString().substring(0, 16)}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _loadReportsData,
            icon: const Icon(Icons.refresh, color: Colors.white, size: 24),
            tooltip: 'تحديث التقارير',
          ),
        ],
      ),
    );
  }

  Widget _buildModernOverviewSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نظرة عامة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // الصف الأول
        Row(
          children: [
            Expanded(
              child: _buildModernStatCard(
                'إجمالي المنتجات',
                '${_productStats['total_products'] ?? 0}',
                Icons.inventory_2_outlined,
                Colors.blue,
                Colors.blue.shade50,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildModernStatCard(
                'إجمالي الفئات',
                '${_categoryStats['active'] ?? 0}',
                Icons.category_outlined,
                Colors.green,
                Colors.green.shade50,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // الصف الثاني
        Row(
          children: [
            Expanded(
              child: _buildModernStatCard(
                'منتجات قليلة',
                '${_productStats['low_stock_count'] ?? 0}',
                Icons.trending_down_outlined,
                Colors.orange,
                Colors.orange.shade50,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildModernStatCard(
                'نفد المخزون',
                '${_productStats['out_of_stock_count'] ?? 0}',
                Icons.warning_outlined,
                Colors.red,
                Colors.red.shade50,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildModernStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    Color backgroundColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 24, color: color),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildInventorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تقرير المخزون',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                _buildInventoryRow(
                  'المنتجات المتوفرة',
                  '${(_productStats['total_products'] ?? 0) - (_productStats['out_of_stock_count'] ?? 0)}',
                  AppTheme.successColor,
                ),
                const Divider(),
                _buildInventoryRow(
                  'المنتجات قليلة الكمية',
                  '${_productStats['low_stock_count'] ?? 0}',
                  AppTheme.warningColor,
                ),
                const Divider(),
                _buildInventoryRow(
                  'المنتجات نافدة المخزون',
                  '${_productStats['out_of_stock_count'] ?? 0}',
                  AppTheme.errorColor,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInventoryRow(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: Theme.of(context).textTheme.bodyLarge),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: color.withValues(alpha: 0.3)),
          ),
          child: Text(
            value,
            style: TextStyle(color: color, fontWeight: FontWeight.bold),
          ),
        ),
      ],
    );
  }

  Widget _buildFinancialSection() {
    final totalValue = _productStats['total_value'] ?? 0.0;
    final totalCost = _productStats['total_cost'] ?? 0.0;
    final expectedProfit = _productStats['expected_profit'] ?? 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التقرير المالي',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                _buildFinancialRow(
                  'إجمالي قيمة المخزون',
                  '${totalValue.toStringAsFixed(2)} ${AppConstants.currencySymbol}',
                  AppTheme.primaryColor,
                ),
                const Divider(),
                _buildFinancialRow(
                  'إجمالي تكلفة المخزون',
                  '${totalCost.toStringAsFixed(2)} ${AppConstants.currencySymbol}',
                  AppTheme.warningColor,
                ),
                const Divider(),
                _buildFinancialRow(
                  'الربح المتوقع',
                  '${expectedProfit.toStringAsFixed(2)} ${AppConstants.currencySymbol}',
                  expectedProfit >= 0
                      ? AppTheme.successColor
                      : AppTheme.errorColor,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFinancialRow(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: Theme.of(context).textTheme.bodyLarge),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  Widget _buildCategorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تقرير الفئات',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                _buildCategoryRow(
                  'إجمالي الفئات',
                  '${_categoryStats['total'] ?? 0}',
                ),
                const Divider(),
                _buildCategoryRow(
                  'الفئات النشطة',
                  '${_categoryStats['active'] ?? 0}',
                ),
                const Divider(),
                _buildCategoryRow(
                  'الفئات التي تحتوي على منتجات',
                  '${_categoryStats['with_products'] ?? 0}',
                ),
                const Divider(),
                _buildCategoryRow(
                  'الفئات الفارغة',
                  '${_categoryStats['empty'] ?? 0}',
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: Theme.of(context).textTheme.bodyLarge),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  // الدوال الحديثة الجديدة
  Widget _buildModernInventorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'تقرير المخزون',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildModernInventoryRow(
                'المنتجات المتوفرة',
                '${(_productStats['total_products'] ?? 0) - (_productStats['out_of_stock_count'] ?? 0)}',
                Colors.green,
                Icons.check_circle_outline,
              ),
              const SizedBox(height: 16),
              _buildModernInventoryRow(
                'المنتجات قليلة الكمية',
                '${_productStats['low_stock_count'] ?? 0}',
                Colors.orange,
                Icons.warning_amber_outlined,
              ),
              const SizedBox(height: 16),
              _buildModernInventoryRow(
                'المنتجات نافدة المخزون',
                '${_productStats['out_of_stock_count'] ?? 0}',
                Colors.red,
                Icons.error_outline,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildModernInventoryRow(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernFinancialSection() {
    final totalValue = _productStats['total_value'] ?? 0.0;
    final totalCost = _productStats['total_cost'] ?? 0.0;
    final expectedProfit = _productStats['expected_profit'] ?? 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'التقرير المالي',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildModernFinancialRow(
                'إجمالي قيمة المخزون',
                '${totalValue.toStringAsFixed(2)} ${AppConstants.currencySymbol}',
                Colors.blue,
                Icons.account_balance_wallet_outlined,
              ),
              const SizedBox(height: 16),
              _buildModernFinancialRow(
                'إجمالي تكلفة المخزون',
                '${totalCost.toStringAsFixed(2)} ${AppConstants.currencySymbol}',
                Colors.orange,
                Icons.shopping_cart_outlined,
              ),
              const SizedBox(height: 16),
              _buildModernFinancialRow(
                'الربح المتوقع',
                '${expectedProfit.toStringAsFixed(2)} ${AppConstants.currencySymbol}',
                expectedProfit >= 0 ? Colors.green : Colors.red,
                expectedProfit >= 0 ? Icons.trending_up : Icons.trending_down,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildModernFinancialRow(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernCategorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'تقرير الفئات',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildModernCategoryRow(
                'إجمالي الفئات',
                '${_categoryStats['total'] ?? 0}',
                Colors.blue,
                Icons.category_outlined,
              ),
              const SizedBox(height: 16),
              _buildModernCategoryRow(
                'الفئات النشطة',
                '${_categoryStats['active'] ?? 0}',
                Colors.green,
                Icons.check_circle_outline,
              ),
              const SizedBox(height: 16),
              _buildModernCategoryRow(
                'الفئات التي تحتوي على منتجات',
                '${_categoryStats['with_products'] ?? 0}',
                Colors.purple,
                Icons.inventory_outlined,
              ),
              const SizedBox(height: 16),
              _buildModernCategoryRow(
                'الفئات الفارغة',
                '${_categoryStats['empty'] ?? 0}',
                Colors.grey,
                Icons.folder_open_outlined,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildModernCategoryRow(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
