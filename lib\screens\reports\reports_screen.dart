import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/providers.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_theme.dart';
import '../../widgets/report_card.dart';

/// شاشة التقارير
/// Reports Screen for viewing analytics and statistics
class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  Map<String, dynamic> _productStats = {};
  Map<String, dynamic> _categoryStats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadReportsData();
  }

  Future<void> _loadReportsData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final productProvider = Provider.of<ProductProvider>(context, listen: false);
      final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);

      final results = await Future.wait([
        productProvider.getProductStatistics(),
        categoryProvider.getCategoryStatistics(),
      ]);

      setState(() {
        _productStats = results[0];
        _categoryStats = results[1];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل التقارير: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReportsData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadReportsData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildOverviewSection(),
                    const SizedBox(height: AppConstants.largePadding),
                    _buildInventorySection(),
                    const SizedBox(height: AppConstants.largePadding),
                    _buildFinancialSection(),
                    const SizedBox(height: AppConstants.largePadding),
                    _buildCategorySection(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildOverviewSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نظرة عامة',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 1.5,
          crossAxisSpacing: AppConstants.defaultPadding,
          mainAxisSpacing: AppConstants.defaultPadding,
          children: [
            ReportCard(
              title: 'إجمالي المنتجات',
              value: '${_productStats['total_products'] ?? 0}',
              icon: Icons.inventory_2,
              color: AppTheme.primaryColor,
            ),
            ReportCard(
              title: 'إجمالي الفئات',
              value: '${_categoryStats['active'] ?? 0}',
              icon: Icons.category,
              color: AppTheme.secondaryColor,
            ),
            ReportCard(
              title: 'منتجات قليلة',
              value: '${_productStats['low_stock_count'] ?? 0}',
              icon: Icons.warning,
              color: AppTheme.warningColor,
            ),
            ReportCard(
              title: 'نفد المخزون',
              value: '${_productStats['out_of_stock_count'] ?? 0}',
              icon: Icons.error,
              color: AppTheme.errorColor,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInventorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تقرير المخزون',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                _buildInventoryRow(
                  'المنتجات المتوفرة',
                  '${(_productStats['total_products'] ?? 0) - (_productStats['out_of_stock_count'] ?? 0)}',
                  AppTheme.successColor,
                ),
                const Divider(),
                _buildInventoryRow(
                  'المنتجات قليلة الكمية',
                  '${_productStats['low_stock_count'] ?? 0}',
                  AppTheme.warningColor,
                ),
                const Divider(),
                _buildInventoryRow(
                  'المنتجات نافدة المخزون',
                  '${_productStats['out_of_stock_count'] ?? 0}',
                  AppTheme.errorColor,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInventoryRow(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFinancialSection() {
    final totalValue = _productStats['total_value'] ?? 0.0;
    final totalCost = _productStats['total_cost'] ?? 0.0;
    final expectedProfit = _productStats['expected_profit'] ?? 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التقرير المالي',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                _buildFinancialRow(
                  'إجمالي قيمة المخزون',
                  '${totalValue.toStringAsFixed(2)} ${AppConstants.currencySymbol}',
                  AppTheme.primaryColor,
                ),
                const Divider(),
                _buildFinancialRow(
                  'إجمالي تكلفة المخزون',
                  '${totalCost.toStringAsFixed(2)} ${AppConstants.currencySymbol}',
                  AppTheme.warningColor,
                ),
                const Divider(),
                _buildFinancialRow(
                  'الربح المتوقع',
                  '${expectedProfit.toStringAsFixed(2)} ${AppConstants.currencySymbol}',
                  expectedProfit >= 0 ? AppTheme.successColor : AppTheme.errorColor,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFinancialRow(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  Widget _buildCategorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تقرير الفئات',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                _buildCategoryRow(
                  'إجمالي الفئات',
                  '${_categoryStats['total'] ?? 0}',
                ),
                const Divider(),
                _buildCategoryRow(
                  'الفئات النشطة',
                  '${_categoryStats['active'] ?? 0}',
                ),
                const Divider(),
                _buildCategoryRow(
                  'الفئات التي تحتوي على منتجات',
                  '${_categoryStats['with_products'] ?? 0}',
                ),
                const Divider(),
                _buildCategoryRow(
                  'الفئات الفارغة',
                  '${_categoryStats['empty'] ?? 0}',
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
