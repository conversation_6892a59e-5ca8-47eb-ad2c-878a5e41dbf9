# تطبيق إدارة المخازن - Stock Management App

تطبيق شامل لإدارة المخازن والمنتجات مطور باستخدام Flutter و Dart، يوفر حلول متكاملة لإدارة المخزون والتحكم في المنتجات.

## 🌟 المميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن للمستخدمين
- نظام صلاحيات متقدم (مدير / موظف)
- إدارة المستخدمين والأدوار

### 📦 إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- دعم الباركود والصور
- تتبع الكميات والأسعار
- تصنيف المنتجات حسب الفئات

### 📊 إدارة الفئات
- إنشاء وتنظيم فئات المنتجات
- ألوان مخصصة للفئات
- إحصائيات شاملة لكل فئة

### 🔍 البحث والتصفية
- بحث متقدم في المنتجات
- تصفية حسب الفئة وحالة المخزون
- ترتيب النتائج بطرق متعددة

### ⚠️ تنبيهات المخزون
- تنبيهات عند انخفاض الكمية
- إشعارات نفاد المخزون
- مراقبة مستمرة للمخزون

### 📈 التقارير والإحصائيات
- تقارير شاملة للمخزون
- إحصائيات المبيعات والأرباح
- تحليل حركة المخزون

### 📋 سجل العمليات
- تتبع جميع عمليات المخزون
- سجل مفصل للإضافة والصرف
- تاريخ كامل للتغييرات

### 📤 التصدير والاستيراد
- تصدير البيانات إلى Excel و CSV
- تقارير قابلة للطباعة
- نسخ احتياطية للبيانات

## 🛠️ التقنيات المستخدمة

### Frontend
- **Flutter**: إطار العمل الرئيسي
- **Dart**: لغة البرمجة
- **Provider**: إدارة الحالة
- **Material Design**: تصميم الواجهات

### Database
- **SQLite**: قاعدة البيانات المحلية
- **Sqflite**: مكتبة قاعدة البيانات

### المكتبات الرئيسية
```yaml
dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.2
  sqflite: ^2.3.3+1
  intl: ^0.19.0
  flutter_barcode_scanner: ^2.0.0
  image_picker: ^1.0.7
  excel: ^4.0.2
  csv: ^6.0.0
  shared_preferences: ^2.2.2
  path_provider: ^2.1.2
  permission_handler: ^11.3.1
```

## 📱 متطلبات النظام

- **Flutter SDK**: 3.0.0 أو أحدث
- **Dart SDK**: 3.0.0 أو أحدث
- **Android**: API level 21 (Android 5.0) أو أحدث
- **iOS**: iOS 11.0 أو أحدث

## 🚀 التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-username/stock-management-app.git
cd stock-management-app
```

### 2. تثبيت التبعيات
```bash
flutter pub get
```

### 3. تشغيل التطبيق
```bash
flutter run
```

## 👥 بيانات تسجيل الدخول الافتراضية

### مدير النظام
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **الصلاحيات**: كاملة

### موظف
- **اسم المستخدم**: `employee`
- **كلمة المرور**: `emp123`
- **الصلاحيات**: محدودة (عرض فقط)

## 📁 هيكل المشروع

```
lib/
├── constants/          # الثوابت والإعدادات
├── database/          # قاعدة البيانات
├── models/            # نماذج البيانات
├── providers/         # إدارة الحالة
├── screens/           # شاشات التطبيق
├── services/          # خدمات البيانات
├── utils/             # الأدوات المساعدة
├── widgets/           # الويدجت المخصصة
└── main.dart          # نقطة دخول التطبيق
```

## 🎨 الواجهات الرئيسية

### 1. شاشة تسجيل الدخول
- تصميم أنيق وبسيط
- التحقق من صحة البيانات
- دعم "تذكرني"

### 2. لوحة التحكم
- إحصائيات سريعة
- تنبيهات المخزون
- إجراءات سريعة

### 3. إدارة المنتجات
- قائمة شاملة بالمنتجات
- بحث وتصفية متقدم
- إضافة وتعديل سهل

### 4. التقارير
- رسوم بيانية تفاعلية
- إحصائيات مفصلة
- تصدير متعدد التنسيقات

## 🔧 الإعدادات والتخصيص

### تخصيص الألوان
يمكن تعديل ألوان التطبيق من ملف `lib/constants/app_theme.dart`

### إعدادات قاعدة البيانات
تعديل إعدادات قاعدة البيانات من ملف `lib/constants/app_constants.dart`

### إضافة لغات جديدة
دعم متعدد اللغات قابل للتوسعة

## 🧪 الاختبار

```bash
# تشغيل الاختبارات
flutter test

# اختبار التكامل
flutter drive --target=test_driver/app.dart
```

## 📦 البناء للإنتاج

### Android
```bash
flutter build apk --release
```

### iOS
```bash
flutter build ios --release
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: https://stockapp.com
- **التوثيق**: https://docs.stockapp.com

## 🙏 شكر وتقدير

- فريق Flutter لإطار العمل الرائع
- مجتمع Dart للمكتبات المفيدة
- جميع المساهمين في المشروع

---

**تم تطوير هذا التطبيق بـ ❤️ باستخدام Flutter**
