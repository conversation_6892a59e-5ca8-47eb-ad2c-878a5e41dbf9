import 'package:flutter/material.dart';

import '../models/models.dart';
import '../services/services.dart';

/// مزود الصلاحيات
/// Permission Provider for managing permissions state
class PermissionProvider with ChangeNotifier {
  final PermissionService _permissionService = PermissionService();

  List<Permission> _permissions = [];
  Map<String, List<Permission>> _categorizedPermissions = {};
  Map<int, bool> _userPermissionStatus = {};
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<Permission> get permissions => _permissions;
  Map<String, List<Permission>> get categorizedPermissions => _categorizedPermissions;
  Map<int, bool> get userPermissionStatus => _userPermissionStatus;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  /// تحميل جميع الصلاحيات
  Future<void> loadPermissions() async {
    _setLoading(true);
    _clearError();

    try {
      _permissions = await _permissionService.getAllPermissions();
      _categorizedPermissions = await _permissionService.getPermissionsByCategory();
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل الصلاحيات: ${e.toString()}');
      _setLoading(false);
    }
  }

  /// تحميل صلاحيات مستخدم معين
  Future<void> loadUserPermissions(int userId) async {
    _setLoading(true);
    _clearError();

    try {
      _userPermissionStatus = await _permissionService.getUserPermissionStatus(userId);
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('خطأ في تحميل صلاحيات المستخدم: ${e.toString()}');
      _setLoading(false);
    }
  }

  /// تحديث صلاحيات المستخدم
  Future<bool> updateUserPermissions(int userId, List<int> permissionIds) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _permissionService.updateUserPermissions(userId, permissionIds);
      if (success) {
        await loadUserPermissions(userId);
      } else {
        _setError('فشل في تحديث الصلاحيات');
      }
      _setLoading(false);
      return success;
    } catch (e) {
      _setError('خطأ في تحديث الصلاحيات: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// منح صلاحية للمستخدم
  Future<bool> grantPermission(int userId, int permissionId) async {
    try {
      final success = await _permissionService.grantPermission(userId, permissionId);
      if (success) {
        _userPermissionStatus[permissionId] = true;
        notifyListeners();
      }
      return success;
    } catch (e) {
      _setError('خطأ في منح الصلاحية: ${e.toString()}');
      return false;
    }
  }

  /// إلغاء صلاحية المستخدم
  Future<bool> revokePermission(int userId, int permissionId) async {
    try {
      final success = await _permissionService.revokePermission(userId, permissionId);
      if (success) {
        _userPermissionStatus[permissionId] = false;
        notifyListeners();
      }
      return success;
    } catch (e) {
      _setError('خطأ في إلغاء الصلاحية: ${e.toString()}');
      return false;
    }
  }

  /// إنشاء صلاحية جديدة
  Future<bool> createPermission(Permission permission) async {
    _setLoading(true);
    _clearError();

    try {
      final permissionId = await _permissionService.createPermission(permission);
      if (permissionId != null) {
        final newPermission = permission.copyWith(id: permissionId);
        _permissions.add(newPermission);
        
        // تحديث الصلاحيات المصنفة
        if (!_categorizedPermissions.containsKey(permission.category)) {
          _categorizedPermissions[permission.category] = [];
        }
        _categorizedPermissions[permission.category]!.add(newPermission);
        
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('فشل في إنشاء الصلاحية');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('خطأ في إنشاء الصلاحية: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// تحديث صلاحية
  Future<bool> updatePermission(Permission permission) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _permissionService.updatePermission(permission);
      if (success) {
        final index = _permissions.indexWhere((p) => p.id == permission.id);
        if (index != -1) {
          _permissions[index] = permission;
          
          // تحديث الصلاحيات المصنفة
          await loadPermissions();
        }
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('فشل في تحديث الصلاحية');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('خطأ في تحديث الصلاحية: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// حذف صلاحية
  Future<bool> deletePermission(int permissionId) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _permissionService.deletePermission(permissionId);
      if (success) {
        _permissions.removeWhere((p) => p.id == permissionId);
        
        // تحديث الصلاحيات المصنفة
        for (final category in _categorizedPermissions.keys) {
          _categorizedPermissions[category]!.removeWhere((p) => p.id == permissionId);
        }
        
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError('فشل في حذف الصلاحية');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('خطأ في حذف الصلاحية: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// نسخ صلاحيات من مستخدم إلى آخر
  Future<bool> copyPermissions(int fromUserId, int toUserId) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _permissionService.copyPermissions(fromUserId, toUserId);
      if (success) {
        await loadUserPermissions(toUserId);
      } else {
        _setError('فشل في نسخ الصلاحيات');
      }
      _setLoading(false);
      return success;
    } catch (e) {
      _setError('خطأ في نسخ الصلاحيات: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// البحث في الصلاحيات
  Future<List<Permission>> searchPermissions(String query) async {
    try {
      return await _permissionService.searchPermissions(query);
    } catch (e) {
      _setError('خطأ في البحث: ${e.toString()}');
      return [];
    }
  }

  /// الحصول على إحصائيات الصلاحيات
  Future<Map<String, dynamic>> getPermissionStats() async {
    try {
      return await _permissionService.getPermissionStats();
    } catch (e) {
      _setError('خطأ في تحميل الإحصائيات: ${e.toString()}');
      return {};
    }
  }

  /// التحقق من وجود صلاحية للمستخدم
  bool hasUserPermission(int permissionId) {
    return _userPermissionStatus[permissionId] ?? false;
  }

  /// تبديل حالة الصلاحية للمستخدم
  Future<bool> toggleUserPermission(int userId, int permissionId) async {
    final hasPermission = _userPermissionStatus[permissionId] ?? false;
    
    if (hasPermission) {
      return await revokePermission(userId, permissionId);
    } else {
      return await grantPermission(userId, permissionId);
    }
  }

  /// الحصول على الصلاحيات المحددة للمستخدم
  List<int> getSelectedPermissionIds() {
    return _userPermissionStatus.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة الخطأ
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// إعادة تعيين حالة المزود
  void reset() {
    _permissions.clear();
    _categorizedPermissions.clear();
    _userPermissionStatus.clear();
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }
}
