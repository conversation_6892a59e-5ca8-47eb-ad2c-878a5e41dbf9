import 'package:flutter/material.dart';

import '../database/database_helper.dart';

/// أداة لإصلاح مشاكل قاعدة البيانات
class DatabaseFixer {
  static final DatabaseHelper _dbHelper = DatabaseHelper();

  /// إصلاح مشكلة الفهارس المكررة
  static Future<bool> fixDuplicateIndexes() async {
    try {
      // إعادة تعيين قاعدة البيانات
      await _dbHelper.resetDatabase();
      return true;
    } catch (e) {
      debugPrint('خطأ في إصلاح قاعدة البيانات: $e');
      return false;
    }
  }

  /// التحقق من صحة قاعدة البيانات
  static Future<bool> validateDatabase() async {
    try {
      // محاولة تنفيذ استعلام بسيط للتحقق من صحة قاعدة البيانات
      await _dbHelper.rawQuery('SELECT 1');
      return true;
    } catch (e) {
      debugPrint('قاعدة البيانات تحتوي على أخطاء: $e');
      return false;
    }
  }

  /// إصلاح قاعدة البيانات إذا كانت تحتوي على أخطاء
  static Future<bool> fixDatabaseIfNeeded() async {
    bool isValid = await validateDatabase();
    if (!isValid) {
      return await fixDuplicateIndexes();
    }
    return true;
  }
}
