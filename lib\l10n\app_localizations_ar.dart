// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'إدارة المخازن';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get products => 'المنتجات';

  @override
  String get categories => 'الفئات';

  @override
  String get reports => 'التقارير';

  @override
  String get settings => 'الإعدادات';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get username => 'اسم المستخدم';

  @override
  String get password => 'كلمة المرور';

  @override
  String get welcome => 'مرحباً';

  @override
  String get totalProducts => 'إجمالي المنتجات';

  @override
  String get categories_count => 'الفئات';

  @override
  String get lowStock => 'كمية قليلة';

  @override
  String get outOfStock => 'نفد المخزون';

  @override
  String get addProduct => 'إضافة منتج';

  @override
  String get addPurchase => 'إضافة مشترى';

  @override
  String get addSale => 'إضافة مبيعة';

  @override
  String get scanBarcode => 'مسح باركود';

  @override
  String get currency => 'العملة';

  @override
  String get language => 'اللغة';

  @override
  String get theme => 'المظهر';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get lightTheme => 'المظهر الفاتح';

  @override
  String get darkTheme => 'المظهر الداكن';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'الإنجليزية';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get add => 'إضافة';

  @override
  String get search => 'بحث';

  @override
  String get filter => 'تصفية';

  @override
  String get sort => 'ترتيب';

  @override
  String get name => 'الاسم';

  @override
  String get description => 'الوصف';

  @override
  String get price => 'السعر';

  @override
  String get quantity => 'الكمية';

  @override
  String get category => 'الفئة';

  @override
  String get barcode => 'الباركود';

  @override
  String get date => 'التاريخ';

  @override
  String get time => 'الوقت';

  @override
  String get total => 'الإجمالي';

  @override
  String get subtotal => 'المجموع الفرعي';

  @override
  String get tax => 'الضريبة';

  @override
  String get discount => 'الخصم';

  @override
  String get customer => 'العميل';

  @override
  String get supplier => 'المورد';

  @override
  String get purchase => 'مشترى';

  @override
  String get sale => 'مبيعة';

  @override
  String get inventory => 'المخزون';

  @override
  String get stock => 'المخزون';

  @override
  String get alert => 'تنبيه';

  @override
  String get warning => 'تحذير';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get info => 'معلومات';

  @override
  String get confirm => 'تأكيد';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get ok => 'موافق';

  @override
  String get close => 'إغلاق';

  @override
  String get back => 'رجوع';

  @override
  String get next => 'التالي';

  @override
  String get previous => 'السابق';

  @override
  String get first => 'الأول';

  @override
  String get last => 'الأخير';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get noData => 'لا توجد بيانات';

  @override
  String get noResults => 'لا توجد نتائج';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get refresh => 'تحديث';

  @override
  String get update => 'تحديث';

  @override
  String get version => 'الإصدار';

  @override
  String get about => 'حول';

  @override
  String get help => 'مساعدة';

  @override
  String get contact => 'اتصل بنا';

  @override
  String get privacy => 'الخصوصية';

  @override
  String get terms => 'الشروط والأحكام';
}
