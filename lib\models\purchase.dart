/// نموذج المشتريات
/// Purchase Model for managing purchase transactions
class Purchase {
  final int? id;
  final String invoiceNumber;
  final String supplierName;
  final String? supplierPhone;
  final String? supplierAddress;
  final double totalAmount;
  final double paidAmount;
  final double remainingAmount;
  final PurchaseStatus status;
  final DateTime purchaseDate;
  final String? notes;
  final int userId;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Purchase({
    this.id,
    required this.invoiceNumber,
    required this.supplierName,
    this.supplierPhone,
    this.supplierAddress,
    required this.totalAmount,
    this.paidAmount = 0.0,
    double? remainingAmount,
    this.status = PurchaseStatus.pending,
    required this.purchaseDate,
    this.notes,
    required this.userId,
    required this.createdAt,
    this.updatedAt,
  }) : remainingAmount = remainingAmount ?? (totalAmount - paidAmount);

  /// تحويل من Map إلى Purchase object
  factory Purchase.fromMap(Map<String, dynamic> map) {
    return Purchase(
      id: map['id'],
      invoiceNumber: map['invoice_number'],
      supplierName: map['supplier_name'],
      supplierPhone: map['supplier_phone'],
      supplierAddress: map['supplier_address'],
      totalAmount: map['total_amount'].toDouble(),
      paidAmount: map['paid_amount'].toDouble(),
      remainingAmount: map['remaining_amount'].toDouble(),
      status: PurchaseStatus.values[map['status']],
      purchaseDate: DateTime.parse(map['purchase_date']),
      notes: map['notes'],
      userId: map['user_id'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at']) 
          : null,
    );
  }

  /// تحويل من Purchase object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'supplier_name': supplierName,
      'supplier_phone': supplierPhone,
      'supplier_address': supplierAddress,
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'remaining_amount': remainingAmount,
      'status': status.index,
      'purchase_date': purchaseDate.toIso8601String(),
      'notes': notes,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من المشترى
  Purchase copyWith({
    int? id,
    String? invoiceNumber,
    String? supplierName,
    String? supplierPhone,
    String? supplierAddress,
    double? totalAmount,
    double? paidAmount,
    double? remainingAmount,
    PurchaseStatus? status,
    DateTime? purchaseDate,
    String? notes,
    int? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Purchase(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      supplierName: supplierName ?? this.supplierName,
      supplierPhone: supplierPhone ?? this.supplierPhone,
      supplierAddress: supplierAddress ?? this.supplierAddress,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      status: status ?? this.status,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      notes: notes ?? this.notes,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من المساواة
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Purchase &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  /// التحقق من كون المشترى مكتمل الدفع
  bool get isFullyPaid => remainingAmount <= 0;

  /// التحقق من كون المشترى مدفوع جزئياً
  bool get isPartiallyPaid => paidAmount > 0 && remainingAmount > 0;

  /// التحقق من كون المشترى غير مدفوع
  bool get isUnpaid => paidAmount <= 0;
}

/// حالات المشتريات
enum PurchaseStatus {
  pending,    // في الانتظار
  completed,  // مكتملة
  cancelled,  // ملغية
}

extension PurchaseStatusExtension on PurchaseStatus {
  String get displayName {
    switch (this) {
      case PurchaseStatus.pending:
        return 'في الانتظار';
      case PurchaseStatus.completed:
        return 'مكتملة';
      case PurchaseStatus.cancelled:
        return 'ملغية';
    }
  }

  String get color {
    switch (this) {
      case PurchaseStatus.pending:
        return '#FF9800'; // برتقالي
      case PurchaseStatus.completed:
        return '#4CAF50'; // أخضر
      case PurchaseStatus.cancelled:
        return '#F44336'; // أحمر
    }
  }
}

/// عنصر المشتريات
/// Purchase Item Model for individual items in a purchase
class PurchaseItem {
  final int? id;
  final int purchaseId;
  final int productId;
  final String productName;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final DateTime createdAt;

  PurchaseItem({
    this.id,
    required this.purchaseId,
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    double? totalPrice,
    required this.createdAt,
  }) : totalPrice = totalPrice ?? (quantity * unitPrice);

  /// تحويل من Map إلى PurchaseItem object
  factory PurchaseItem.fromMap(Map<String, dynamic> map) {
    return PurchaseItem(
      id: map['id'],
      purchaseId: map['purchase_id'],
      productId: map['product_id'],
      productName: map['product_name'],
      quantity: map['quantity'],
      unitPrice: map['unit_price'].toDouble(),
      totalPrice: map['total_price'].toDouble(),
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  /// تحويل من PurchaseItem object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'purchase_id': purchaseId,
      'product_id': productId,
      'product_name': productName,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من عنصر المشترى
  PurchaseItem copyWith({
    int? id,
    int? purchaseId,
    int? productId,
    String? productName,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
    DateTime? createdAt,
  }) {
    return PurchaseItem(
      id: id ?? this.id,
      purchaseId: purchaseId ?? this.purchaseId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// التحقق من المساواة
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PurchaseItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
