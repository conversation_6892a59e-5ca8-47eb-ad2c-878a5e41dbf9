import 'package:flutter/material.dart';

/// شاشة اختبار بسيطة
class TestScreen extends StatelessWidget {
  const TestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تطبيق إدارة المخازن'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2,
              size: 100,
              color: Colors.blue,
            ),
            SizedBox(height: 20),
            Text(
              'مرحباً بك في تطبيق إدارة المخازن',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 10),
            Text(
              'التطبيق يعمل بنجاح!',
              style: TextStyle(
                fontSize: 18,
                color: Colors.green,
              ),
            ),
            Sized<PERSON><PERSON>(height: 30),
            Card(
              margin: EdgeInsets.all(20),
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  children: [
                    Text(
                      'بيانات تسجيل الدخول الافتراضية:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 10),
                    Text('المدير: admin / admin123'),
                    Text('الموظف: employee / emp123'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
