import '../constants/app_constants.dart';

/// أدوات التحقق من صحة البيانات
/// Data Validation Utilities
class Validators {
  /// التحقق من الحقول المطلوبة
  static String? required(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return fieldName != null 
          ? '$fieldName مطلوب'
          : AppConstants.requiredFieldMessage;
    }
    return null;
  }

  /// التحقق من البريد الإلكتروني
  static String? email(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value.trim())) {
      return AppConstants.invalidEmailMessage;
    }
    return null;
  }

  /// التحقق من كلمة المرور
  static String? password(String? value, {int minLength = 6}) {
    if (value == null || value.isEmpty) {
      return AppConstants.requiredFieldMessage;
    }
    
    if (value.length < minLength) {
      return 'كلمة المرور يجب أن تكون على الأقل $minLength أحرف';
    }
    
    return null;
  }

  /// التحقق من تطابق كلمات المرور
  static String? confirmPassword(String? value, String? originalPassword) {
    if (value == null || value.isEmpty) {
      return AppConstants.requiredFieldMessage;
    }
    
    if (value != originalPassword) {
      return AppConstants.confirmPasswordMessage;
    }
    
    return null;
  }

  /// التحقق من الأرقام
  static String? number(String? value, {bool required = true}) {
    if (!required && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredFieldMessage;
    }
    
    if (double.tryParse(value.trim()) == null) {
      return AppConstants.invalidNumberMessage;
    }
    
    return null;
  }

  /// التحقق من الأرقام الصحيحة
  static String? integer(String? value, {bool required = true, int? min, int? max}) {
    if (!required && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredFieldMessage;
    }
    
    final intValue = int.tryParse(value.trim());
    if (intValue == null) {
      return AppConstants.invalidNumberMessage;
    }
    
    if (min != null && intValue < min) {
      return 'القيمة يجب أن تكون على الأقل $min';
    }
    
    if (max != null && intValue > max) {
      return 'القيمة يجب أن تكون أقل من أو تساوي $max';
    }
    
    return null;
  }

  /// التحقق من الأرقام العشرية
  static String? decimal(String? value, {bool required = true, double? min, double? max}) {
    if (!required && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredFieldMessage;
    }
    
    final doubleValue = double.tryParse(value.trim());
    if (doubleValue == null) {
      return AppConstants.invalidNumberMessage;
    }
    
    if (min != null && doubleValue < min) {
      return 'القيمة يجب أن تكون على الأقل $min';
    }
    
    if (max != null && doubleValue > max) {
      return 'القيمة يجب أن تكون أقل من أو تساوي $max';
    }
    
    return null;
  }

  /// التحقق من الأسعار
  static String? price(String? value, {bool required = true}) {
    final result = decimal(value, required: required, min: 0);
    if (result != null) return result;
    
    if (value != null && value.trim().isNotEmpty) {
      final price = double.parse(value.trim());
      if (price < 0) {
        return 'السعر يجب أن يكون أكبر من أو يساوي صفر';
      }
    }
    
    return null;
  }

  /// التحقق من الكمية
  static String? quantity(String? value, {bool required = true}) {
    final result = integer(value, required: required, min: 0);
    if (result != null) return result;
    
    return null;
  }

  /// التحقق من الباركود
  static String? barcode(String? value, {bool required = false}) {
    if (!required && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (required && (value == null || value.trim().isEmpty)) {
      return AppConstants.requiredFieldMessage;
    }
    
    if (value != null && value.trim().isNotEmpty) {
      // التحقق من طول الباركود
      if (value.trim().length < 8 || value.trim().length > 20) {
        return 'الباركود يجب أن يكون بين 8 و 20 رقم';
      }
      
      // التحقق من أن الباركود يحتوي على أرقام فقط
      if (!RegExp(r'^[0-9]+$').hasMatch(value.trim())) {
        return 'الباركود يجب أن يحتوي على أرقام فقط';
      }
    }
    
    return null;
  }

  /// التحقق من اسم المستخدم
  static String? username(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredFieldMessage;
    }
    
    if (value.trim().length < 3) {
      return 'اسم المستخدم يجب أن يكون على الأقل 3 أحرف';
    }
    
    if (value.trim().length > 20) {
      return 'اسم المستخدم يجب أن يكون أقل من 20 حرف';
    }
    
    // التحقق من أن اسم المستخدم يحتوي على أحرف وأرقام فقط
    if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value.trim())) {
      return 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط';
    }
    
    return null;
  }

  /// التحقق من الاسم الكامل
  static String? fullName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredFieldMessage;
    }
    
    if (value.trim().length < 2) {
      return 'الاسم يجب أن يكون على الأقل حرفين';
    }
    
    if (value.trim().length > 50) {
      return 'الاسم يجب أن يكون أقل من 50 حرف';
    }
    
    return null;
  }

  /// التحقق من اسم الفئة
  static String? categoryName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredFieldMessage;
    }
    
    if (value.trim().length < 2) {
      return 'اسم الفئة يجب أن يكون على الأقل حرفين';
    }
    
    if (value.trim().length > 30) {
      return 'اسم الفئة يجب أن يكون أقل من 30 حرف';
    }
    
    return null;
  }

  /// التحقق من اسم المنتج
  static String? productName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppConstants.requiredFieldMessage;
    }
    
    if (value.trim().length < 2) {
      return 'اسم المنتج يجب أن يكون على الأقل حرفين';
    }
    
    if (value.trim().length > 100) {
      return 'اسم المنتج يجب أن يكون أقل من 100 حرف';
    }
    
    return null;
  }

  /// التحقق من الوصف
  static String? description(String? value, {int maxLength = 500}) {
    if (value != null && value.trim().isNotEmpty) {
      if (value.trim().length > maxLength) {
        return 'الوصف يجب أن يكون أقل من $maxLength حرف';
      }
    }
    
    return null;
  }

  /// التحقق من رقم الهاتف
  static String? phoneNumber(String? value, {bool required = false}) {
    if (!required && (value == null || value.trim().isEmpty)) {
      return null;
    }
    
    if (required && (value == null || value.trim().isEmpty)) {
      return AppConstants.requiredFieldMessage;
    }
    
    if (value != null && value.trim().isNotEmpty) {
      // إزالة المسافات والرموز
      final cleanPhone = value.replaceAll(RegExp(r'[^\d+]'), '');
      
      // التحقق من طول رقم الهاتف
      if (cleanPhone.length < 10 || cleanPhone.length > 15) {
        return 'رقم الهاتف غير صحيح';
      }
      
      // التحقق من تنسيق رقم الهاتف
      if (!RegExp(r'^(\+?[0-9]{10,15})$').hasMatch(cleanPhone)) {
        return 'رقم الهاتف غير صحيح';
      }
    }
    
    return null;
  }

  /// دمج عدة مُحققات
  static String? combine(String? value, List<String? Function(String?)> validators) {
    for (final validator in validators) {
      final result = validator(value);
      if (result != null) return result;
    }
    return null;
  }
}
